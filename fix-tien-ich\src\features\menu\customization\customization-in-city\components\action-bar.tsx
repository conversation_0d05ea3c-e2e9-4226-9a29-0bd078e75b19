import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { Button, Input } from '@/components/ui'

interface City {
  id: string
  city_name: string
}

interface ActionBarProps {
  searchQuery: string
  onSearchQueryChange: (value: string) => void
  onSearchKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void
  selectedCityId: string
  onCityChange: (cityId: string) => void
  cities: City[]
  onExportCustomizations: () => void
  onImportCustomizations: () => void
  onCreateCustomization: () => void
}

export function ActionBar({
  searchQuery,
  onSearchQueryChange,
  onSearchKeyDown,
  selectedCityId,
  onCityChange,
  cities,
  onExportCustomizations,
  onImportCustomizations,
  onCreateCustomization
}: ActionBarProps) {
  return (
    <div className='mb-6 flex items-center justify-between'>
      <div className='flex items-center gap-4'>
        <h2 className='text-xl font-semibold'>Customization</h2>
        <Input
          placeholder='Tìm kiếm customization'
          className='w-64'
          value={searchQuery}
          onChange={e => onSearchQueryChange(e.target.value)}
          onKeyDown={onSearchKeyDown}
        />
        <Select value={selectedCityId} onValueChange={onCityChange}>
          <SelectTrigger className='w-48'>
            <SelectValue placeholder='Chọn thành phố' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả thành phố</SelectItem>
            {cities.map(city => (
              <SelectItem key={city.id} value={city.id}>
                {city.city_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className='flex items-center gap-4'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size='sm' variant='outline'>
              Tiện ích
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={onExportCustomizations}>
              Xuất, Chi tiết customization
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onImportCustomizations}>
              Thêm customization từ file
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button size='sm' variant='default' onClick={onCreateCustomization}>
          Tạo customization
        </Button>
      </div>
    </div>
  )
}
