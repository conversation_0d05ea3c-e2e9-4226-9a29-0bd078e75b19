import { useEffect, useState } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import type { MenuItem } from '@/types/api/menu-items'
import { Upload, X, Plus } from 'lucide-react'

import { useCrmItemTypeOptions } from '@/hooks/api/use-crm-item-types'
import { useItemsData } from '@/hooks/api/use-items'

import { EditSheet } from '@/components/ui/edit-sheet'

import { MultiSelectDropdown } from '@/components/multi-select-dropdown'
import { Combobox } from '@/components/pos/combobox'
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Textarea,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Checkbox
} from '@/components/ui'

const formSchema = z.object({
  item_id: z.string().min(1, 'Mã món không được để trống'),
  item_name: z.string().min(1, 'Tên món không được để trống'),
  description: z.string().optional(),
  ots_price: z.number().min(0, 'Giá tại chỗ phải >= 0'),
  ta_price: z.number().min(0, 'Giá mang về phải >= 0'),
  sort: z.number().min(0, 'Thứ tự hiển thị phải >= 0'),
  item_type_id: z.string().min(1, 'Nhóm món không được để trống'),
  customizations: z.array(z.string()).optional(),
  allow_take_away: z.boolean(),
  allow_self_order: z.boolean(),
  is_eat_with: z.boolean(),
  item_image_path: z.string().optional()
})

type MenuItemFormData = z.infer<typeof formSchema>

interface CustomizationOption {
  Name: string
  Min_Permitted: number
  Max_Permitted: number
  LstItem_Id: string[]
}

interface CustomizationData {
  LstItem_Options: CustomizationOption[]
}

interface MenuItemEditDialogProps {
  item: MenuItem | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (data: MenuItemFormData) => void
  isLoading?: boolean
}

interface CustomizationSectionProps {
  customizationData?: string
  isEatWith: boolean
}

function CustomizationSection({ customizationData, isEatWith }: CustomizationSectionProps) {
  const { data: itemsData } = useItemsData()

  const parseCustomizationData = (data?: string): CustomizationOption[] => {
    if (!data) return []
    try {
      const parsed = JSON.parse(data) as CustomizationData
      return parsed.LstItem_Options || []
    } catch (error) {
      return []
    }
  }

  const [customizations, setCustomizations] = useState<CustomizationOption[]>(() => {
    const parsed = parseCustomizationData(customizationData)
    return parsed.length > 0 ? parsed : []
  })

  useEffect(() => {
    const parsed = parseCustomizationData(customizationData)
    setCustomizations(parsed)
  }, [customizationData])

  const addCustomization = () => {
    setCustomizations([...customizations, { Name: '', Min_Permitted: 0, Max_Permitted: 0, LstItem_Id: [] }])
  }

  const removeCustomization = (index: number) => {
    setCustomizations(customizations.filter((_, i) => i !== index))
  }

  const updateCustomization = (index: number, field: keyof CustomizationOption, value: string | number) => {
    setCustomizations(customizations.map((c, i) => (i === index ? { ...c, [field]: value } : c)))
  }

  const removeItemFromCustomization = (customizationIndex: number, itemId: string) => {
    setCustomizations(
      customizations.map((c, i) =>
        i === customizationIndex ? { ...c, LstItem_Id: c.LstItem_Id.filter(id => id !== itemId) } : c
      )
    )
  }

  if (isEatWith) {
    return null
  }

  return (
    <div className='w-full space-y-6'>
      {customizations.length > 0 && (
        <>
          <h3 className='text-sm font-semibold text-gray-700'>CUSTOMIZE</h3>
          {customizations.map((customization, index) => (
            <div key={index} className='rounded-lg border border-gray-200 bg-gray-50 p-5'>
              <div className='flex items-center'>
                <Button
                  type='button'
                  variant='ghost'
                  size='sm'
                  onClick={() => removeCustomization(index)}
                  className='ml-auto h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-700'
                >
                  <X className='h-4 w-4' />
                </Button>
              </div>
              <div className='flex items-start justify-center gap-4'>
                <div className='flex w-full flex-col gap-2'>
                  <Input
                    value={customization.Name}
                    onChange={e => updateCustomization(index, 'Name', e.target.value)}
                    placeholder='Tên nhóm'
                    className='h-10'
                  />
                  <Input
                    type='number'
                    value={customization.Min_Permitted === 0 ? '' : customization.Min_Permitted}
                    onChange={e => updateCustomization(index, 'Min_Permitted', Number(e.target.value))}
                    placeholder='Yêu cầu'
                    className='h-10'
                  />
                  <Input
                    type='number'
                    value={customization.Max_Permitted === 0 ? '' : customization.Max_Permitted}
                    onChange={e => updateCustomization(index, 'Max_Permitted', Number(e.target.value))}
                    placeholder='Tối đa'
                    className='h-10'
                  />
                </div>

                <div className='w-full'>
                  <div className='item-center flex justify-between'>
                    <label className='block text-sm font-semibold text-gray-700'>
                      DANH SÁCH MÓN <span className='text-red-600'>*</span>
                    </label>
                  </div>
                  <div className='min-h-[100px] rounded-lg border border-gray-200 bg-white p-4'>
                    <div className='flex flex-wrap gap-2'>
                      {customization.LstItem_Id.length > 0 ? (
                        customization.LstItem_Id.map(itemId => {
                          const item = itemsData?.find(item => item.item_id === itemId)
                          const itemName = item?.item_name || itemId

                          return (
                            <span
                              key={itemId}
                              className='inline-flex items-center gap-2 rounded-full bg-blue-100 px-3 py-1.5 text-sm text-blue-800'
                            >
                              {itemName}
                              <X
                                className='h-3 w-3 cursor-pointer transition-colors hover:text-red-500'
                                onClick={() => removeItemFromCustomization(index, itemId)}
                              />
                            </span>
                          )
                        })
                      ) : (
                        <p className='text-sm text-gray-500'>Chưa có món nào được chọn</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </>
      )}
      <Button
        type='button'
        variant='outline'
        size='sm'
        onClick={addCustomization}
        className='flex h-9 items-center gap-2'
      >
        <Plus className='h-4 w-4' />
        Thêm customize
      </Button>
    </div>
  )
}

export function MenuItemEditDialog({ item, open, onOpenChange, onSave, isLoading }: MenuItemEditDialogProps) {
  const { data: itemsData = [] } = useItemsData()
  const { options: itemTypeOptions, isLoading: isLoadingItemTypes } = useCrmItemTypeOptions()

  const form = useForm<MenuItemFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      item_id: '',
      item_name: '',
      description: '',
      ots_price: 0,
      ta_price: 0,
      sort: 1000,
      item_type_id: '',
      customizations: [],
      allow_take_away: true,
      allow_self_order: true,
      is_eat_with: false,
      item_image_path: ''
    }
  })

  const handleImageUpload = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = async e => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        try {
          const imageUrl = URL.createObjectURL(file)
          form.setValue('item_image_path', imageUrl)
        } catch (error) {}
      }
    }
    input.click()
  }

  const handleImageRemove = () => {
    form.setValue('item_image_path', '')
  }

  useEffect(() => {
    if (item && open) {
      let customizationsArray: string[] = []
      if (item.Customizations) {
        try {
          const parsed = JSON.parse(item.Customizations)
          if (parsed.LstItem_Options && Array.isArray(parsed.LstItem_Options)) {
            customizationsArray = parsed.LstItem_Options.flatMap((option: any) =>
              Array.isArray(option.LstItem_Id) ? option.LstItem_Id : []
            )
          }
        } catch (error) {}
      }

      form.reset({
        item_id: item.Item_Id,
        item_name: item.Item_Name,
        description: item.Description || '',
        ots_price: item.Ots_Price,
        ta_price: item.Ta_Price,
        sort: item.Sort,
        item_type_id: item.Item_Type_Id,
        customizations: customizationsArray,
        allow_take_away: item.Allow_Take_Away === 1,
        allow_self_order: item.Allow_Self_Order === 1,
        is_eat_with: item.Is_Eat_With === 1,
        item_image_path: item.Item_Image_Path || ''
      })
    }
  }, [item, open, form])

  const handleSubmit = (data: MenuItemFormData) => {
    const customizationsString =
      data.customizations && data.customizations.length > 0
        ? JSON.stringify({
            LstItem_Options: [
              {
                id: 'CUS_GROUP_DEFAULT',
                Name: 'Món ăn kèm',
                LstItem_Id: data.customizations,
                Max_Permitted: 0,
                Min_Permitted: 0
              }
            ]
          })
        : ''

    const submitData = {
      ...data,
      customizations: customizationsString
    }

    onSave(submitData as any)
  }

  const handleClose = () => {
    form.reset()
    onOpenChange(false)
  }

  const handleSave = () => {
    form.handleSubmit(handleSubmit)()
  }

  if (!item) return null

  return (
    <EditSheet
      open={open}
      onOpenChange={onOpenChange}
      title='CHỈNH SỬA'
      isLoading={isLoading}
      onSave={handleSave}
      onCancel={handleClose}
    >
      <div className='p-6'>
        <div className='mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4'>
          <p className='flex items-center gap-3 text-sm text-yellow-800'>
            <span className='flex h-5 w-5 items-center justify-center rounded-full bg-yellow-500 text-xs font-bold text-white'>
              !
            </span>
            Nội dung hiện thi sẽ được cập nhật trên 1 cửa hàng
          </p>
        </div>

        <Form {...form}>
          <form className='space-y-6'>
            <div className='grid grid-cols-1 gap-8 lg:grid-cols-2'>
              <div className='space-y-6'>
                <div className='rounded-lg border border-gray-200 bg-gray-50 p-4'>
                  <div className='grid grid-cols-3 gap-4 text-sm'>
                    <div>
                      <span className='block font-medium text-gray-600'>MÃ MÓN</span>
                      <div className='mt-2 font-mono font-semibold text-blue-600'>{item.Item_Id}</div>
                    </div>
                    <div>
                      <span className='block font-medium text-gray-600'>GIÁ TẠI CHỖ</span>
                      <div className='mt-2 font-semibold text-green-600'>
                        {item.Ots_Price.toLocaleString('vi-VN')} đ
                      </div>
                    </div>
                    <div>
                      <span className='block font-medium text-gray-600'>GIÁ MANG VỀ</span>
                      <div className='mt-2 font-semibold text-green-600'>{item.Ta_Price.toLocaleString('vi-VN')} đ</div>
                    </div>
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name='item_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-semibold text-gray-700'>TÊN MÓN *</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder='Nhập tên món' className='h-10' />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-semibold text-gray-700'>MÔ TẢ</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={4} placeholder='Nhập mô tả món ăn' className='resize-none' />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='sort'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-semibold text-gray-700'>THỨ TỰ HIỂN THỊ</FormLabel>
                      <FormControl>
                        <Input
                          type='number'
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                          className='h-10'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className='space-y-4'>
                  <FormField
                    control={form.control}
                    name='allow_self_order'
                    render={({ field }) => (
                      <FormItem className='flex flex-row items-center space-y-0 space-x-3'>
                        <FormControl>
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                        <FormLabel className='text-sm font-medium text-gray-700'>Bán tại chỗ</FormLabel>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='allow_take_away'
                    render={({ field }) => (
                      <FormItem className='flex flex-row items-center space-y-0 space-x-3'>
                        <FormControl>
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                        <FormLabel className='text-sm font-medium text-gray-700'>Bán mang đi</FormLabel>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='space-y-6'>
                <div className='space-y-4'>
                  <div className='flex h-48 w-full items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50'>
                    {form.watch('item_image_path') ? (
                      <img
                        src={form.watch('item_image_path')}
                        alt={form.watch('item_name')}
                        className='h-full w-full rounded-lg object-cover'
                      />
                    ) : (
                      <div className='text-center'>
                        <Upload className='mx-auto h-12 w-12 text-gray-400' />
                        <p className='mt-2 text-sm text-gray-500'>Tải ảnh lên</p>
                      </div>
                    )}
                  </div>
                  <div className='flex gap-2'>
                    <Button type='button' variant='outline' size='sm' className='flex-1' onClick={handleImageUpload}>
                      <Upload className='mr-2 h-4 w-4' />
                      Tải ảnh lên
                    </Button>
                    <Button type='button' variant='outline' size='sm' onClick={handleImageRemove}>
                      <X className='h-4 w-4' />
                    </Button>
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name='item_type_id'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-semibold text-gray-700'>NHÓM MÓN</FormLabel>
                      <FormControl>
                        <Combobox
                          options={itemTypeOptions.map(option => ({
                            value: option.value,
                            label: option.label
                          }))}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder={isLoadingItemTypes ? 'Đang tải...' : 'Chọn nhóm món'}
                          searchPlaceholder='Tìm kiếm nhóm món...'
                          emptyText='Không tìm thấy nhóm món'
                          disabled={isLoadingItemTypes}
                          className='w-full'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='is_eat_with'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-semibold text-gray-700'>LOẠI MÓN</FormLabel>
                      <Select
                        onValueChange={value => field.onChange(value === 'eat_with')}
                        value={field.value ? 'eat_with' : 'normal'}
                      >
                        <FormControl>
                          <SelectTrigger className='h-10 w-full'>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='normal'>Món thường</SelectItem>
                          <SelectItem value='eat_with'>Món ăn kèm</SelectItem>
                          <SelectItem value='parent'>Món cha</SelectItem>
                          <SelectItem value='child'>Món con</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='customizations'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-semibold text-gray-700'>DANH SÁCH MÓN ĂN KÈM</FormLabel>
                      <FormControl>
                        <MultiSelectDropdown
                          options={itemsData.map(item => ({
                            value: item.item_id,
                            label: item.item_name
                          }))}
                          value={field.value || []}
                          onValueChange={field.onChange}
                          placeholder='Chọn món ăn kèm...'
                          searchPlaceholder='Tìm kiếm món ăn...'
                          emptyText='Không tìm thấy món ăn'
                          className='h-10'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <CustomizationSection customizationData={item?.Customizations} isEatWith={form.watch('is_eat_with')} />
          </form>
        </Form>
      </div>
    </EditSheet>
  )
}
