import { useState } from 'react'
import { <PERSON><PERSON>, Card, CardContent } from '@/components/ui'
import { DeviceDetailModal } from './components'

const deviceTypes = [
  {
    id: 'pos',
    name: 'Thi<PERSON><PERSON> bị <PERSON>',
    description: 'Thi<PERSON><PERSON> bị bán hàng đáp ứng các nghiệp vụ kinh doanh nhà hàng.',
    image:
      'https://image.foodbook.vn/images/20210114/1610594463910-iAP200-600x600.jpg',
    images: [
      'https://image.foodbook.vn/images/20210114/1610594463910-iAP200-600x600.jpg',
      'https://image.foodbook.vn/images/20210115/1610679782642-POS1.png',
      'https://image.foodbook.vn/images/20210115/1610679798609-POS2.png',
      'https://image.foodbook.vn/images/20210115/1610679809909-POS3.png',
      'https://image.foodbook.vn/images/20210115/1610679823366-POS4.png',
    ],
    detailInformation: [
      'Thi<PERSON><PERSON> bị bán hàng đáp ứng các nghiệp vụ kinh doanh nhà hàng.',
    ],
  },
  {
    id: 'pos-mini',
    name: 'Thiết bị POS MINI',
    description:
      'Thiết bị bán hàng cầm tay nhỏ gọn, phù hợp cho nhà hàng vừa và nhỏ.',
    image:
      'https://image.foodbook.vn/images/20210114/1610594509383-B06-2-600x600.jpg',
    images: [
      'https://image.foodbook.vn/images/20210114/1610594509383-B06-2-600x600.jpg',
      'https://image.foodbook.vn/images/20210114/1610618634292-POS_MINI_0.png',
      'https://image.foodbook.vn/images/20210114/1610618649865-POS_MINI_1.png',
      'https://image.foodbook.vn/images/20210114/1610618664516-POS_MINI_2.png',
    ],
    detailInformation: [
      'Thiết bị bán hàng cầm tay nhỏ gọn, phù hợp cho nhà hàng vừa và nhỏ.',
      'Giao diện được tối ưu theo chiều dọc.',
    ],
  },
  {
    id: 'pda',
    name: 'Thiết bị PDA',
    description:
      'Thiết bị cầm tay cá nhân được tích hợp chức năng order giúp nhân viên thuận tiện đặt món cho khách.',
    image:
      'https://image.foodbook.vn/images/20210114/1610594699308-TAB-A8-1-1-600x600.png',
    images: [
      'https://image.foodbook.vn/images/20210114/1610594699308-TAB-A8-1-1-600x600.png',
      'https://image.foodbook.vn/images/20210115/1610679414089-PDA1.png',
      'https://image.foodbook.vn/images/20210115/1610679436448-PDA2.png',
    ],
    detailInformation: [
      'Thiết bị cầm tay cá nhân được tích hợp chức năng order giúp nhân viên thuận tiện đặt món cho khách.',
      'PDA Fabi chỉ dùng để oder không có chức năng thanh toán.',
      'Để sử dụng PDA cần phải chọn 1 POS làm máy chủ.',
    ],
  },
  {
    id: 'kds',
    name: 'Thiết bị KDS',
    description:
      'Thiết bị hiển thị danh sách món order dành cho bếp, dùng để thay thế máy in order, theo dõi tình hình chế biến món ăn trực quan, tức thời.',
    image: 'https://image.foodbook.vn/images/20210114/1610596408975-KDS.png',
    images: [
      'https://image.foodbook.vn/images/20210114/1610596408975-KDS.png',
      'https://image.foodbook.vn/images/20210115/1610677692502-KDS1.png',
      'https://image.foodbook.vn/images/20210115/1610677784222-KDS2.png',
      'https://image.foodbook.vn/images/20210115/1610677754310-KDS3.png',
      'https://image.foodbook.vn/images/20210115/1610677857417-KDS4.png',
    ],
    detailInformation: [
      'Thiết bị hiển thị danh sách món order dành cho bếp, dùng để thay thế máy in order, theo dõi tình hình chế biến món ăn trực quan, tức thời.',
    ],
  },
  {
    id: 'kds-maker',
    name: 'Thiết bị KDS MAKER',
    description:
      'Thiết bị giúp đầu bếp, nhân viên pha chế nhận đồ và chế biến, kết hợp với in tem, in phiếu trả đồ.',
    image: 'https://image.foodbook.vn/images/20210114/1610596408975-KDS.png',
    images: [
      'https://image.foodbook.vn/images/20210114/1610596408975-KDS.png',
      'https://image.foodbook.vn/images/20210115/1610678602088-KDS5.png',
    ],
    detailInformation: [
      'Thiết bị giúp đầu bếp, nhân viên pha chế nhận đồ và chế biến, kết hợp với in tem, in phiếu trả đồ.',
      'KDS MAKER kết hợp với KDS ORDER CONTROL để hiển thị món, hoá đơn đã hoàn thành.',
    ],
  },
  {
    id: 'kds-order-control',
    name: 'Thiết bị KDS ORDER CONTROL',
    description:
      'Thiết bị phục vụ cho việc quản lý trả đồ hoặc thay thế máy in order, phù hợp cho mô hình bán mang về.',
    image: 'https://image.foodbook.vn/images/20210114/1610596408975-KDS.png',
    images: [
      'https://image.foodbook.vn/images/20210114/1610596408975-KDS.png',
      'https://image.foodbook.vn/images/20210115/1610678602088-KDS5.png',
    ],
    detailInformation: [
      'Thiết bị phục vụ cho việc quản lý trả đồ hoặc thay thế máy in order, phù hợp cho mô hình bán mang về.',
      'Thường sử dụng kết hợp KDS ORDER CONTROL với KDS MAKER cho mô hình bán mang về, kitchen cloud.',
    ],
  },
  {
    id: 'self-order',
    name: 'Thiết bị SELF ORDER',
    description:
      'Thiết bị hỗ trợ khách hàng có thể tự đặt đồ, lựa chọn sản phẩm và thanh toán.',
    image:
      'https://image.foodbook.vn/images/20210114/1610594725602-may-self-order-co-chan-PNG.png',
    images: [
      'https://image.foodbook.vn/images/20210114/1610594725602-may-self-order-co-chan-PNG.png',
      'https://image.foodbook.vn/images/20210115/1610678935514-SO-1.png',
    ],
    detailInformation: [
      'Thiết bị hỗ trợ khách hàng có thể tự đặt đồ, lựa chọn sản phẩm và thanh toán.',
      'Nhờ hình thức này, nhà hàng/quán café có thể tiết kiệm chi phí nhân sự, tối ưu thời gian phục vụ.',
    ],
  },
]

export default function DeviceTypesPage() {
  const [selectedDevice, setSelectedDevice] = useState<
    (typeof deviceTypes)[0] | null
  >(null)
  const [modalOpen, setModalOpen] = useState(false)

  const handleDeviceClick = (device: (typeof deviceTypes)[0]) => {
    setSelectedDevice(device)
    setModalOpen(true)
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
        {deviceTypes.map((deviceType) => (
          <Card
            key={deviceType.id}
            className='flex h-full flex-col overflow-hidden'
          >
            <div className='relative h-48 bg-gray-100'>
              <div
                className='absolute inset-0 bg-contain bg-center bg-no-repeat'
                style={{
                  backgroundImage: `url('${deviceType.image}')`,
                }}
              />
            </div>
            <CardContent className='flex flex-1 flex-col p-6 text-center'>
              <h5 className='mb-2 text-lg font-semibold'>{deviceType.name}</h5>
              <p className='text-muted-foreground mb-4 line-clamp-2 flex-1 text-sm leading-relaxed'>
                {deviceType.description}
              </p>
              <Button
                variant='default'
                size='sm'
                className='px-3 py-1 text-xs'
                onClick={() => handleDeviceClick(deviceType)}
              >
                Chi tiết
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <DeviceDetailModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        device={
          selectedDevice
            ? {
                id: selectedDevice.id,
                name: selectedDevice.name,
                description: selectedDevice.description,
                images: selectedDevice.images || [selectedDevice.image],
                detailInformation: selectedDevice.detailInformation,
              }
            : null
        }
      />
    </div>
  )
}
