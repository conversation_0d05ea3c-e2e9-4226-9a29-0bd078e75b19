const sourcesData = [
  {
    name: 'TẠI CHỖ',
    orders: 1245,
    revenue: 156780000,
    color: '#0088FE'
  },
  {
    name: 'ShopeFood',
    orders: 987,
    revenue: 134560000,
    color: '#00C49F'
  },
  {
    name: 'GRABFOOD',
    orders: 856,
    revenue: 118900000,
    color: '#FFBB28'
  },
  {
    name: 'BE_FOOD',
    orders: 743,
    revenue: 98450000,
    color: '#FF8042'
  },
  {
    name: 'MANG VỀ',
    orders: 612,
    revenue: 87320000,
    color: '#8884D8'
  }
]

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

export function TopSourcesList() {
  return (
    <div className='space-y-4'>
      {sourcesData.map((source, index) => (
        <div key={index} className='flex items-center justify-between'>
          <div className='flex items-center gap-3'>
            <div 
              className='h-4 w-4 rounded-full' 
              style={{ backgroundColor: source.color }}
            />
            <div>
              <p className='font-medium text-sm'>{source.name}</p>
              <p className='text-xs text-muted-foreground'>{source.orders} đơn hàng</p>
            </div>
          </div>
          <div className='text-right'>
            <p className='font-bold text-sm'>{formatCurrency(source.revenue)}</p>
          </div>
        </div>
      ))}
    </div>
  )
}
