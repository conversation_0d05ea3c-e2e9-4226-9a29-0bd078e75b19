import { ChevronDown, ChevronRight } from 'lucide-react'

import { PosModal } from '@/components/pos'
import {
  Checkbox,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Input
} from '@/components/ui'

interface DishItem {
  id: string
  item_name: string
  ots_price: number
}

interface DishSelectionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
  onConfirm: () => void
  dishSearchTerm: string
  setDishSearchTerm: (term: string) => void
  selectedSectionOpen: boolean
  setSelectedSectionOpen: (open: boolean) => void
  remainingSectionOpen: boolean
  setRemainingSectionOpen: (open: boolean) => void
  selectedDishes: Set<string>
  handleDishToggle: (itemId: string) => void
  selectedDishItems: DishItem[]
  remainingDishItems: DishItem[]
}

export function DishSelectionModal({
  open,
  onOpenChange,
  onCancel,
  onConfirm,
  dishSearchTerm,
  setDishSearchTerm,
  selectedSectionOpen,
  setSelectedSectionOpen,
  remainingSectionOpen,
  setRemainingSectionOpen,
  selectedDishes,
  handleDishToggle,
  selectedDishItems,
  remainingDishItems
}: DishSelectionModalProps) {
  return (
    <PosModal
      title='Chọn món áp dụng customization'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      confirmText='Xác nhận'
      cancelText='Hủy'
      maxWidth='sm:max-w-2xl'
    >
      <div className='space-y-4'>
        <Input
          placeholder='Tìm kiếm'
          value={dishSearchTerm}
          onChange={e => setDishSearchTerm(e.target.value)}
          className='w-full'
        />

        <Collapsible
          open={selectedSectionOpen}
          onOpenChange={setSelectedSectionOpen}
        >
          <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
            <span className='font-medium'>Đã chọn ({selectedDishItems.length})</span>
            {selectedSectionOpen ? (
              <ChevronDown className='h-4 w-4' />
            ) : (
              <ChevronRight className='h-4 w-4' />
            )}
          </CollapsibleTrigger>
          <CollapsibleContent className='mt-2'>
            <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
              {selectedDishItems.length === 0 ? (
                <p className='text-sm text-gray-500'>Chưa có món nào được chọn</p>
              ) : (
                selectedDishItems.map(item => (
                  <label
                    key={item.id}
                    className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                  >
                    <Checkbox
                      checked={selectedDishes.has(item.id)}
                      onCheckedChange={() => handleDishToggle(item.id)}
                    />
                    <div className='flex-1'>
                      <p className='text-sm font-medium'>{item.item_name}</p>
                      <p className='text-xs text-gray-500'>
                        {item.ots_price.toLocaleString('vi-VN')} đ
                      </p>
                    </div>
                  </label>
                ))
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>

        <Collapsible
          open={remainingSectionOpen}
          onOpenChange={setRemainingSectionOpen}
        >
          <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
            <span className='font-medium'>Còn lại ({remainingDishItems.length})</span>
            {remainingSectionOpen ? (
              <ChevronDown className='h-4 w-4' />
            ) : (
              <ChevronRight className='h-4 w-4' />
            )}
          </CollapsibleTrigger>
          <CollapsibleContent className='mt-2'>
            <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
              {remainingDishItems.length === 0 ? (
                <p className='text-sm text-gray-500'>Không có món nào</p>
              ) : (
                remainingDishItems.map(item => (
                  <label
                    key={item.id}
                    className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                  >
                    <Checkbox
                      checked={selectedDishes.has(item.id)}
                      onCheckedChange={() => handleDishToggle(item.id)}
                    />
                    <div className='flex-1'>
                      <p className='text-sm font-medium'>{item.item_name}</p>
                      <p className='text-xs text-gray-500'>
                        {item.ots_price.toLocaleString('vi-VN')} đ
                      </p>
                    </div>
                  </label>
                ))
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </PosModal>
  )
}
