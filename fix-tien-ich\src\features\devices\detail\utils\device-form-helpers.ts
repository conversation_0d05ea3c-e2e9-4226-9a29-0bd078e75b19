import {
  DEVICE_TYPE_OPTIONS,
  DEVICE_STATUS_OPTIONS,
  DEVICE_TYPE_LOCAL_OPTIONS,
  TYPE_ALLOW_CONNECT_POS_OPTIONS,
  KDS_NOTIFICATION_OPTIONS
} from '../constants/device-form-constants'

export const getDeviceTypeLabel = (apiValue: string): string => {
  const option = DEVICE_TYPE_OPTIONS.find(opt => opt.apiValue === apiValue)
  return option?.value ?? 'POS'
}

export const getDeviceTypeApiValue = (label: string): string => {
  const option = DEVICE_TYPE_OPTIONS.find(opt => opt.value === label)
  return option?.apiValue ?? 'POS'
}

export const getDeviceStatusLabel = (apiValue: number): string => {
  const option = DEVICE_STATUS_OPTIONS.find(opt => opt.apiValue === apiValue)
  return option?.value ?? 'Hoạt động'
}

export const getDeviceStatusApiValue = (label: string): number => {
  const option = DEVICE_STATUS_OPTIONS.find(opt => opt.value === label)
  return option?.apiValue ?? 1
}

export const getDeviceTypeLocalLabel = (apiValue: number): string => {
  const option = DEVICE_TYPE_LOCAL_OPTIONS.find(opt => opt.apiValue === apiValue)
  return option?.value ?? 'None'
}

export const getDeviceTypeLocalApiValue = (label: string): number => {
  const option = DEVICE_TYPE_LOCAL_OPTIONS.find(opt => opt.value === label)
  return option?.apiValue ?? 0
}

export const getTypeAllowConnectPosLabel = (apiValue: number): string => {
  const option = TYPE_ALLOW_CONNECT_POS_OPTIONS.find(opt => opt.apiValue === apiValue)
  return option?.value ?? 'Cho phép'
}

export const getTypeAllowConnectPosApiValue = (label: string): number => {
  const option = TYPE_ALLOW_CONNECT_POS_OPTIONS.find(opt => opt.value === label)
  return option?.apiValue ?? 0
}

export const getKdsNotificationLabel = (apiValue: number): string => {
  const option = KDS_NOTIFICATION_OPTIONS.find(opt => opt.apiValue === apiValue)
  return option?.value ?? 'Không hiển thị'
}

export const getKdsNotificationApiValue = (label: string): number => {
  const option = KDS_NOTIFICATION_OPTIONS.find(opt => opt.value === label)
  return option?.apiValue ?? 0
}

export const formatDeviceData = (deviceData: any, extraData: any) => {
  return {
    deviceName: deviceData?.device_name || deviceData?.name || '',
    deviceCode: deviceData?.device_code || deviceData?.serialNumber || '',
    deviceType: deviceData?.type || '',
    storeLocation: deviceData?.storeName || 'N/A',
    localIpAddress: deviceData?.my_ip_local || 'N/A',
    version: deviceData?.version_app || deviceData?.version || 'N/A',
    timezone: deviceData?.time_zone || 'N/A',
    lastUpdate: (() => {
      const updatedAt =
        deviceData?.updated_at || deviceData?.lastUpdate || deviceData?.updatedAt || extraData?.updated_at

      if (!updatedAt) {
        return 'N/A'
      }

      const date = updatedAt instanceof Date ? updatedAt : new Date(updatedAt)

      if (isNaN(date.getTime())) {
        return String(updatedAt)
      }

      const day = date.getDate().toString().padStart(2, '0')
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const year = date.getFullYear()
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')

      return `${day}/${month}/${year} ${hours}:${minutes}`
    })(),
    isActive: deviceData?.active === 1,
    enableTabManagement: extraData?.area_manager_enable === 1,
    displayColumns: extraData?.column_table ?? '5',
    enableScreen2: extraData?.dual_screen_enable === 1,
    useItemInStore: extraData?.allow_print_label === 1,
    kdsNotificationConfig: getKdsNotificationLabel(extraData?.enable_tab_kds ?? 0),
    enablePosNutMode: extraData?.enable_cash_drawer === 1,
    typeAllowConnectPos: getTypeAllowConnectPosLabel(extraData?.type_allow_connect_pos ?? 0),
    enableComboGroup: Array.isArray(extraData?.item_type_ignore) && extraData.item_type_ignore.length > 0,
    enableTabDisplay: extraData?.enable_tab_order_ta === 1,
    deviceTypeLocal: getDeviceTypeLocalLabel(extraData?.device_type_local ?? 0),
    newIpAddress: '',
    specialConfigType: extraData?.special_character || ''
  }
}

export const buildUpdatePayload = (
  formData: any,
  deviceData: any,
  selectedGroups?: Set<string>,
  selectedCombos?: Set<string>,
  itemTypesData?: any[],
  combosData?: any[]
) => {
  const updateData: any = {
    ...deviceData,

    device_name: formData.deviceName,
    type: getDeviceTypeApiValue(formData.deviceType),
    active: formData.isActive ? 1 : 0,

    company_uid: deviceData.company_uid,
    brand_uid: deviceData.brand_uid,
    store_uid: deviceData.store_uid,

    extra_data: {
      ...deviceData.extra_data,

      area_manager_enable: formData.enableTabManagement ? 1 : 0,
      enable_tab_management: formData.enableTabManagement ? 1 : 0,
      column_table: formData.displayColumns || '6',
      display_columns: formData.displayColumns || '6',
      dual_screen_enable: formData.enableScreen2 ? 1 : 0,
      enable_screen_2: formData.enableScreen2 ? 1 : 0,
      allow_print_label: formData.useItemInStore ? 1 : 0,
      use_item_in_store: formData.useItemInStore ? 1 : 0,
      enable_cash_drawer: formData.enablePosNutMode ? 1 : 0,
      enable_tab_order_ta: formData.enableTabDisplay ? 1 : 0,
      enable_tab_kds: getKdsNotificationApiValue(formData.kdsNotificationConfig),
      type_allow_connect_pos: getTypeAllowConnectPosApiValue(formData.typeAllowConnectPos),
      device_type_local: getDeviceTypeLocalApiValue(formData.deviceTypeLocal),
      special_character: formData.specialConfigType || '',
      new_restaurant_interface: formData.enableTabDisplay ? 1 : 0,
      item_type_ignore: (() => {
        if (formData.extra_data?.item_type_ignore && Array.isArray(formData.extra_data.item_type_ignore)) {
          return formData.extra_data.item_type_ignore
        }

        if (selectedGroups && selectedCombos && itemTypesData && combosData) {
          const itemTypeIgnore: string[] = []

          selectedGroups.forEach(groupId => {
            const foundGroup = itemTypesData.find(group => group.id === groupId)
            if (foundGroup && foundGroup.item_type_id) {
              itemTypeIgnore.push(foundGroup.item_type_id)
            }
          })

          selectedCombos.forEach(comboId => {
            const foundCombo = combosData.find(combo => combo.id === comboId)
            if (foundCombo && foundCombo.package_id) {
              itemTypeIgnore.push(foundCombo.package_id)
            }
          })

          return itemTypeIgnore
        }

        return formData.enableComboGroup ? formData.extra_data?.item_type_ignore || [] : []
      })()
    }
  }

  if (formData.timezone && formData.timezone !== 'N/A') {
    updateData.extra_data.timezone = formData.timezone
  }

  if (formData.newIpAddress && formData.newIpAddress.trim()) {
    updateData.address = formData.newIpAddress.trim()
  }

  return updateData
}

export const buildCreatePayload = (
  formData: any,
  storeUid: string,
  _selectedGroups: Set<string>,
  _selectedCombos: Set<string>
) => {
  return {
    deviceName: formData.deviceName,
    storeId: storeUid,
    deviceType: getDeviceTypeApiValue(formData.deviceType),
    brandUid: ''
  }
}
