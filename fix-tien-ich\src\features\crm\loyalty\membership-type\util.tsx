import type { MembershipTypeItem } from '@/types/api/crm'

export const formatCurrency = (amount: number) => {
  return `${amount.toLocaleString('vi-VN')} VND`
}

export const formatPercentage = (rate: number) => {
  return `${(rate * 100).toFixed(6)}%`
}

export const formatUpgradeCondition = (amount: number | string, isNoChange?: number) => {
  // Nếu is_no_change = 1, hiển thị empty cell
  if (isNoChange === 1) return ''

  const numAmount = typeof amount === 'string' ? parseFloat(amount) || 0 : amount
  if (numAmount === 0) return 'Thành viên đăng ký lần đầu'
  return `Tiền tích lũy vượt ${formatCurrency(numAmount)}`
}

export const formatReviewCondition = (item: MembershipTypeItem) => {
  if (item.upgrade_amount && item.downgrade_amount && item.downgrade_to_level) {
    const downgradeLevel = item.downgrade_to_level

    const line1 = `Định mức chi tiêu xét lại hạng: ${item.downgrade_amount}`
    const line2 = `Nếu không đạt: Hạ về hạng ${downgradeLevel}`

    return `${line1}\n${line2}`
  }
  return ''
}

export const formatTime = (createdAt: string, updatedAt?: string) => {
  const formatDateTime = (dateStr: string) => {
    const date = new Date(dateStr)
    return `Ngày tạo: ${date.toLocaleDateString('vi-VN')} ${date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })}`
  }

  let timeText = formatDateTime(createdAt)

  if (updatedAt && updatedAt !== createdAt) {
    timeText += `\nNgày cập nhật: ${updatedAt}`
  }

  return timeText
}

export const getStatusInfo = (active: number) => {
  if (active === 1) {
    return {
      text: 'Đang hoạt động',
      className: 'bg-green-100 text-green-800'
    }
  }
  return {
    text: 'Đã hủy',
    className: 'bg-red-100 text-red-800'
  }
}
