import { useCallback } from 'react'

import { <PERSON> } from '@tanstack/react-router'

import { Header } from '@/components/layout/header'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { But<PERSON> } from '@/components/ui'

import { AccountTable } from './components/account-table'
import { LocalUser } from './data'
import { useAccountManagement } from './hooks'
import { ACCOUNT_ROUTES, navigateToEditUser } from './utils/navigation'

const ErrorDisplay = ({ error }: { error: string }) => (
  <div className='container mx-auto px-4 py-8'>
    <div className='py-8 text-center'>
      <p className='text-red-600'>{error}</p>
    </div>
  </div>
)

const CreateAccountButton = () => (
  <div className='mb-6'>
    <Link to={ACCOUNT_ROUTES.CREATE}>
      <Button>T<PERSON><PERSON> tài k<PERSON>n</Button>
    </Link>
  </div>
)

export default function AccountPage() {
  const { users, isLoading, error, toggleUserStatus } = useAccountManagement()

  const handleEditUser = useCallback((user: LocalUser) => {
    navigateToEditUser(user.id)
  }, [])

  const handleToggleStatus = useCallback(
    async (userId: string) => {
      await toggleUserStatus(userId)
    },
    [toggleUserStatus]
  )

  if (error) {
    return <ErrorDisplay error={error} />
  }

  return (
    <>
      <Header>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>
      <div className='container mx-auto px-4 py-8'>
        <CreateAccountButton />
        <AccountTable
          users={users}
          isLoading={isLoading}
          onEditUser={handleEditUser}
          onToggleStatus={handleToggleStatus}
        />
      </div>
    </>
  )
}
