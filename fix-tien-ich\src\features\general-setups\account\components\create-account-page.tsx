import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Checkbox
} from '@/components/ui'

import { CreateUserFormData } from '../data'
import { useCreateAccountForm, useAccountManagement } from '../hooks'

export default function CreateAccountPage() {
  const navigate = useNavigate()
  const { form, resetForm } = useCreateAccountForm()
  const { createUser, permissionCategories, isLoading } = useAccountManagement()
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])

  const handleSubmit = async (data: CreateUserFormData) => {
    try {
      await createUser({ ...data, permissions: selectedPermissions })
      resetForm()
      setSelectedPermissions([])
      navigate({ to: '/general-setups/account' })
    } catch (error) {
      console.error('Error creating account:', error)
    }
  }

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    setSelectedPermissions(prev => {
      if (checked) {
        return [...prev, permissionId]
      } else {
        return prev.filter(id => id !== permissionId)
      }
    })

    // Update form value
    form.setValue(
      'permissions',
      checked ? [...selectedPermissions, permissionId] : selectedPermissions.filter(id => id !== permissionId)
    )
  }

  const handleCancel = () => {
    resetForm()
    setSelectedPermissions([])
    navigate({ to: '/general-setups/account' })
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-6'>
        <h1 className='text-2xl font-semibold'>Tạo tài khoản mới</h1>
      </div>

      <div className='max-w-4xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <FormField
                control={form.control}
                name='username'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên người dùng *</FormLabel>
                    <FormControl>
                      <Input placeholder='Nhập tên người dùng' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder='Nhập email' type='email' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='password'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mật khẩu *</FormLabel>
                  <FormControl>
                    <Input placeholder='Nhập mật khẩu' type='password' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <FormLabel className='text-base font-medium'>Cài đặt quyền cho tài khoản *</FormLabel>
              <div className='mt-4 space-y-6'>
                {permissionCategories.map(category => (
                  <div key={category.name} className='space-y-3'>
                    <h4 className='text-sm font-medium'>{category.name}</h4>
                    <div className='grid grid-cols-1 gap-2 md:grid-cols-2'>
                      {category.permissions.map(permission => (
                        <div key={permission.id} className='flex items-center space-x-2'>
                          <Checkbox
                            id={permission.id}
                            checked={selectedPermissions.includes(permission.id)}
                            onCheckedChange={checked => handlePermissionChange(permission.id, checked as boolean)}
                          />
                          <label
                            htmlFor={permission.id}
                            className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                          >
                            {permission.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
              {form.formState.errors.permissions && (
                <p className='text-destructive mt-2 text-sm'>{form.formState.errors.permissions.message}</p>
              )}
            </div>

            <div className='flex justify-end gap-3'>
              <Button type='button' variant='outline' onClick={handleCancel}>
                Hủy
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Đang tạo...' : 'Tạo tài khoản'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  )
}
