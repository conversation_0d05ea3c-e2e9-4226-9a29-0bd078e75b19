#!/bin/bash

set -e

# Configuration
DISCORD_WEBHOOK=${DISCORD_WEBHOOK:-"https://discord.com/api/webhooks/1399246272694849566/1FWCnnx4HYR7ifUWMJGytSDwBIU-iadAq1Tu0OowYrLT9_gZdMllZbGfo22b9gG74mZs"}
REPO_OWNER=$(gh repo view --json owner --jq '.owner.login')
REPO_NAME=$(gh repo view --json name --jq '.name')
TODAY=$(date +%Y%m%d)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Check if gh CLI is installed and authenticated
check_prerequisites() {
    if ! command -v gh &> /dev/null; then
        error "GitHub CLI (gh) is not installed. Please install it first."
        exit 1
    fi

    if ! gh auth status &> /dev/null; then
        error "GitHub CLI is not authenticated. Please run 'gh auth login' first."
        exit 1
    fi

    if ! command -v jq &> /dev/null; then
        error "jq is not installed. Please install it first."
        exit 1
    fi
}

# Send Discord notification
send_discord_notification() {
    local message="$1"
    local color="$2"
    
    local payload=$(cat <<EOF
{
    "embeds": [{
        "title": "GitHub Issue Alert",
        "description": "$message",
        "color": $color,
        "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.000Z)",
        "footer": {
            "text": "GitHub Issue Monitor"
        }
    }]
}
EOF
)

    if curl -s -H "Content-Type: application/json" \
            -d "$payload" \
            "$DISCORD_WEBHOOK" > /dev/null; then
        log "Discord notification sent successfully"
    else
        error "Failed to send Discord notification"
    fi
}

# Post a comment to a GitHub issue
post_github_comment() {
    local issue_number="$1"
    local comment_body="$2"

    log "Posting comment to issue #$issue_number..."
    if gh issue comment "$issue_number" -b "$comment_body"; then
        log "Successfully posted comment to issue #$issue_number"
    else
        error "Failed to post comment to issue #$issue_number"
    fi
}

# Check if issue has parent (is linked to another issue)
has_parent_issue() {
    local issue_number="$1"

    log "Checking if issue #$issue_number has a parent issue..."
    
    # Method 1: Check via GitHub API if any other issue tracks this one as a sub-issue
    # This is more efficient than scanning all issue bodies
    local all_issues
    if all_issues=$(gh api repos/"$REPO_OWNER"/"$REPO_NAME"/issues --paginate --jq '.[] | select(.state == "open") | {number: .number, sub_issues_summary: .sub_issues_summary}' 2>/dev/null); then
        log "Checking if any open issue tracks #$issue_number as a sub-issue"
        
        # Check each issue to see if it has sub-issues and could be tracking this one
        echo "$all_issues" | jq -c '.' 2>/dev/null | while read -r issue; do
            local other_number=$(echo "$issue" | jq -r '.number' 2>/dev/null || echo "")
            local sub_issues_total=$(echo "$issue" | jq -r '.sub_issues_summary.total // 0' 2>/dev/null || echo "0")
            
            # Skip if this is the same issue or if it has no sub-issues
            if [ "$other_number" = "$issue_number" ] || [ "$sub_issues_total" -eq 0 ]; then
                continue
            fi
            
            # If an issue has sub-issues, we need to check if our issue is one of them
            # Unfortunately, GitHub API doesn't directly expose the sub-issue list,
            # so we'll fall back to checking the issue body and timeline
            if [ "$sub_issues_total" -gt 0 ]; then
                local other_body
                if other_body=$(gh api repos/"$REPO_OWNER"/"$REPO_NAME"/issues/"$other_number" --jq '.body // ""' 2>/dev/null); then
                    # Check for task list references to our issue
                    if echo "$other_body" | grep -qE "(\- \[[ x]\].*#${issue_number}[^0-9]|\- \[[ x]\].*#${issue_number}$|\- \[[ x]\].*/${issue_number}[^0-9]|\- \[[ x]\].*/${issue_number}$)"; then
                        log "Issue #$issue_number is tracked as a sub-issue by issue #$other_number"
                        return 0 # Has parent
                    fi
                fi
            fi
        done
    else
        warn "Could not access GitHub API to check for sub-issue tracking"
    fi
    
    # Method 2: Check GitHub timeline for parent_issue_added events
    log "Checking timeline for parent_issue_added events"
    local timeline_check
    if timeline_check=$(gh api repos/"$REPO_OWNER"/"$REPO_NAME"/issues/"$issue_number"/timeline --jq '.[] | select(.event == "parent_issue_added")' 2>/dev/null); then
        if [ -n "$timeline_check" ]; then
            log "Issue #$issue_number has a parent issue (found parent_issue_added event)"
            return 0 # Has parent
        fi
    fi
    
    # Method 3: Check if this issue's body contains parent indicators
    local issue_body
    if issue_body=$(gh issue view "$issue_number" --json body --jq '.body // ""' 2>/dev/null); then
        # Check if the issue body contains "Part of" or "Child of" indicators
        if echo "$issue_body" | grep -qiE "(part of|child of|subtask of|tracked by|parent.*#[0-9]+)"; then
            log "Issue #$issue_number appears to be a subtask based on its body content"
            return 0 # Has parent
        fi
    fi
    
    # Method 4: Fallback - check if any other issue's body references this issue
    # This is a slower method but more comprehensive for older tracking methods
    local quick_check_issues
    if quick_check_issues=$(gh issue list --state open --json number,body --limit 30 2>/dev/null); then
        log "Quick scan of recent issues for references to #$issue_number"
        
        # Use temporary file to avoid subshell return issues
        local temp_file=$(mktemp)
        echo "$quick_check_issues" | jq -c '.[]' 2>/dev/null > "$temp_file"
        
        while IFS= read -r issue; do
            local other_number=$(echo "$issue" | jq -r '.number' 2>/dev/null || echo "")
            local other_body=$(echo "$issue" | jq -r '.body // ""' 2>/dev/null || echo "")
            
            # Skip if this is the same issue
            if [ "$other_number" = "$issue_number" ]; then
                continue
            fi
            
            # Check for various reference patterns in the body
            if echo "$other_body" | grep -qE "(#${issue_number}[^0-9]|#${issue_number}$|/${issue_number}[^0-9]|/${issue_number}$|closes.*#${issue_number}|fixes.*#${issue_number}|resolves.*#${issue_number})"; then
                log "Issue #$issue_number is referenced by issue #$other_number (potential parent)"
                rm -f "$temp_file"
                return 0 # Has parent
            fi
        done < "$temp_file"
        
        rm -f "$temp_file"
    fi
    
    log "No parent issue detected for #$issue_number"
    return 1  # No parent
}

# Validate YYYYMMDD format
is_valid_date_format() {
    local date_str="$1"
    
    # Check if it matches YYYYMMDD pattern
    if [[ ! "$date_str" =~ ^[0-9]{8}$ ]]; then
        return 1
    fi
    
    # Validate if it's a real date (cross-platform)
    if [[ "$(uname)" == "Darwin" ]]; then
        # macOS
        if date -j -f "%Y%m%d" "$date_str" >/dev/null 2>&1; then
            return 0
        else
            return 1
        fi
    else
        # Linux (GNU date)
        if date -d "$date_str" >/dev/null 2>&1; then
            return 0
        else
            return 1
        fi
    fi
}

# Main function to check issues
check_issues() {
    log "Fetching open issues from $REPO_OWNER/$REPO_NAME..."
    
    # Get all open issues. If the command fails or returns empty, default to an empty JSON array.
    local issues
    if ! issues=$(gh issue list --state open --json number,title,url --limit 100 2>/dev/null); then
        warn "Could not fetch issues, they may be disabled for this repository."
        issues="[]"
    elif [ -z "$issues" ]; then
        issues="[]"
    fi
    local issue_count=$(echo "$issues" | jq '. | length')
    
    log "Found $issue_count open issues"
    
    if [ "$issue_count" -eq 0 ]; then
        log "No open issues found"
        return 0
    fi
    
    local notifications_sent=0
    
    # Process each issue
    echo "$issues" | jq -c '.[]' | while read -r issue; do
        local number=$(echo "$issue" | jq -r '.number')
        local title=$(echo "$issue" | jq -r '.title')
        local url=$(echo "$issue" | jq -r '.url')
        
        log "Checking issue #$number: $title"
        
        # Skip issues with parent
        if has_parent_issue "$number"; then
            log "Issue #$number has a parent issue, skipping deadline check"
            continue
        fi
        
        # Check if title starts with YYYYMMDD
        if [[ "$title" =~ ^([0-9]{8}) ]]; then
            local issue_date="${BASH_REMATCH[1]}"
            
            if is_valid_date_format "$issue_date"; then
                if [ "$issue_date" -lt "$TODAY" ]; then
                    warn "Issue #$number is overdue (date: $issue_date, today: $TODAY)"
                    local discord_message="🚨 **Overdue Issue Alert**\n\n**[$title]($url)**\n\nIssue date: $issue_date\nToday: $TODAY\n\nThis issue is past its due date and needs attention."
                    send_discord_notification "$discord_message" 15158332  # Red color
                    
                    local github_comment="This issue is overdue. The deadline was **$issue_date**. Please provide an explanation for the delay or update the deadline in the title."
                    post_github_comment "$number" "$github_comment"
                    
                    ((notifications_sent++))
                else
                    log "Issue #$number is not overdue yet"
                fi
            else
                warn "Issue #$number has invalid date format: $issue_date"
                local discord_message="⚠️ **Invalid Date Format**\n\n**[$title]($url)**\n\nThe issue title contains an invalid date format: $issue_date\nPlease use YYYYMMDD format."
                send_discord_notification "$discord_message" 15105570  # Orange color
                ((notifications_sent++))
            fi
        else
            warn "Issue #$number does not start with YYYYMMDD format"
            local discord_message="📋 **Issue Missing Date Prefix**\n\n**[$title]($url)**\n\nThis issue does not start with YYYYMMDD format.\nPlease consider adding a date prefix if this is a scheduled task."
            send_discord_notification "$discord_message" 3447003   # Blue color
            
            local github_comment="This issue does not have a deadline in the \`YYYYMMDD\` format in its title. Please add a deadline to help with tracking."
            post_github_comment "$number" "$github_comment"
            
            ((notifications_sent++))
        fi
    done
    
    log "Issue check completed. Notifications sent: $notifications_sent"
}

# Main execution
main() {
    log "Starting GitHub issue check..."
    
    check_prerequisites
    check_issues
    
    log "GitHub issue check completed successfully"
}

# Run main function
main "$@"