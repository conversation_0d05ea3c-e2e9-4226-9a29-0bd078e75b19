import { useCallback } from 'react'
import { UseFormReturn } from 'react-hook-form'

import { CreateUserFormData } from '../data'

export const usePermissionManagement = (form: UseFormReturn<CreateUserFormData>) => {
  const togglePermission = useCallback((permissionId: string, isChecked: boolean) => {
    const currentPermissions = form.getValues('permissions') || []
    
    const updatedPermissions = isChecked 
      ? [...currentPermissions, permissionId]
      : currentPermissions.filter(id => id !== permissionId)
    
    form.setValue('permissions', updatedPermissions)
  }, [form])

  const isPermissionSelected = useCallback((permissionId: string): boolean => {
    const currentPermissions = form.getValues('permissions') || []
    return currentPermissions.includes(permissionId)
  }, [form])

  const clearAllPermissions = useCallback(() => {
    form.setValue('permissions', [])
  }, [form])

  const selectAllPermissions = useCallback((allPermissionIds: string[]) => {
    form.setValue('permissions', allPermissionIds)
  }, [form])

  return {
    togglePermission,
    isPermissionSelected,
    clearAllPermissions,
    selectAllPermissions
  }
}
