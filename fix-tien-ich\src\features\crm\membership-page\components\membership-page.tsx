import { useState } from 'react'

import { useSearch } from '@tanstack/react-router'

import { usePosParentSettings, useRegisterPageConfig } from '@/hooks/crm'

import { Button, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

export default function MembershipPage() {
  const search = useSearch({ strict: false }) as { pos_parent?: string }
  const posParent = search.pos_parent || 'BRAND-953H'

  const [formData, setFormData] = useState({
    phone: '',
    name: '',
    birthday: '',
    gender: '',
    email: '',
    address: ''
  })

  const { data: registerConfig, isLoading: isConfigLoading } = useRegisterPageConfig({ pos_parent: posParent })
  const { data: settingsData, isLoading: isSettingsLoading } = usePosParentSettings({ pos_parent: posParent })

  const isLoading = isConfigLoading || isSettingsLoading

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Form submitted:', formData)
  }

  if (isLoading) {
    return (
      <div className='flex min-h-screen items-center justify-center bg-gray-100'>
        <div className='text-center'>
          <div className='mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600'></div>
          <p className='mt-4 text-gray-600'>Đang tải...</p>
        </div>
      </div>
    )
  }

  const config = registerConfig?.data
  const settings = settingsData

  const banner = config?.banner || settings?.image || ''
  const logo = config?.logo || settings?.Logo_Image || ''
  const brandName = settings?.name || 'Tutimi-Bình Lợi'

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-100 p-4'>
      <div className='w-full max-w-md overflow-hidden rounded-lg bg-white p-4 shadow-lg'>
        <div className='relative h-48 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600'>
          {banner && <img src={banner} alt='Header' className='h-full w-full rounded-lg object-cover' />}

          <div className='absolute bottom-4 left-4 flex items-center space-x-2'>
            {logo && <img src={logo} alt='Logo' className='h-8 w-8 rounded bg-white' />}
            <span className='text-sm font-semibold text-white'>{brandName}</span>
          </div>
        </div>

        <div className='p-4'>
          <h1 className='mb-6 text-center text-2xl font-bold text-gray-800'>Đăng Ký Thành Viên</h1>

          <form onSubmit={handleSubmit} className='space-y-4'>
            <div>
              <Input
                id='phone'
                type='tel'
                placeholder='Nhập số điện thoại của bạn'
                value={formData.phone}
                onChange={e => handleInputChange('phone', e.target.value)}
                required
                className='mt-1'
              />
            </div>

            {(() => {
              const activeFields = config?.form_data?.filter(field => field.active && field.field_id !== 'phone') || []
              const renderedFields: React.ReactNode[] = []
              let skipNext = false

              activeFields.forEach((field, index) => {
                if (skipNext) {
                  skipNext = false
                  return
                }

                const renderField = (fieldData: any) => {
                  switch (fieldData.field_id) {
                    case 'name':
                      return (
                        <Input
                          id={fieldData.field_id}
                          type='text'
                          placeholder='Họ và tên'
                          value={formData.name}
                          onChange={e => handleInputChange('name', e.target.value)}
                          required={fieldData.require}
                          className='mt-1'
                        />
                      )

                    case 'birthday':
                      return (
                        <Input
                          id={fieldData.field_id}
                          type='date'
                          placeholder='Chọn ngày sinh'
                          value={formData.birthday}
                          onChange={e => handleInputChange('birthday', e.target.value)}
                          required={fieldData.require}
                          className='mt-1'
                        />
                      )

                    case 'gender':
                      return (
                        <Select value={formData.gender} onValueChange={value => handleInputChange('gender', value)}>
                          <SelectTrigger className='mt-1'>
                            <SelectValue placeholder='Chọn giới tính' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='male'>Nam</SelectItem>
                            <SelectItem value='female'>Nữ</SelectItem>
                            <SelectItem value='other'>Khác</SelectItem>
                          </SelectContent>
                        </Select>
                      )

                    case 'email':
                      return (
                        <Input
                          id={fieldData.field_id}
                          type='email'
                          placeholder='Email'
                          value={formData.email}
                          onChange={e => handleInputChange('email', e.target.value)}
                          required={fieldData.require}
                          className='mt-1'
                        />
                      )

                    case 'address':
                      return (
                        <Input
                          id={fieldData.field_id}
                          type='text'
                          placeholder='Địa chỉ'
                          value={formData.address}
                          onChange={e => handleInputChange('address', e.target.value)}
                          required={fieldData.require}
                          className='mt-1'
                        />
                      )

                    default:
                      return (
                        <Input
                          id={fieldData.field_id}
                          type='text'
                          placeholder={fieldData.field_name}
                          required={fieldData.require}
                          className='mt-1'
                        />
                      )
                  }
                }

                const nextField = activeFields[index + 1]
                const isBirthdayGenderPair =
                  (field.field_id === 'birthday' && nextField?.field_id === 'gender') ||
                  (field.field_id === 'gender' && nextField?.field_id === 'birthday')

                if (isBirthdayGenderPair) {
                  const birthdayField = field.field_id === 'birthday' ? field : nextField
                  const genderField = field.field_id === 'gender' ? field : nextField

                  renderedFields.push(
                    <div key={`${birthdayField.field_id}-${genderField.field_id}`} className='grid grid-cols-2 gap-3'>
                      <div>{renderField(birthdayField)}</div>
                      <div>{renderField(genderField)}</div>
                    </div>
                  )
                  skipNext = true
                } else {
                  renderedFields.push(<div key={field.field_id}>{renderField(field)}</div>)
                }
              })

              return renderedFields
            })()}

            <Button type='submit' className='w-full bg-blue-600 hover:bg-blue-700'>
              Xác Thực Thông Tin
            </Button>
          </form>
        </div>
      </div>
    </div>
  )
}
