import { IconChevronDown, IconChevronUp } from '@tabler/icons-react'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Checkbox, Button } from '@/components/ui'
import { useTableSelector } from '../../hooks'

interface TableSelectorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedTables: string[]
  onSave: (selectedTables: string[]) => void
  storeId: string
  brandId: string
}

export function TableSelector({
  open,
  onOpenChange,
  selectedTables,
  onSave,
  storeId,
  brandId,
}: TableSelectorProps) {
  const {
    // Data
    areaData,
    isLoading,

    // Expansion state
    expandedAreas,
    toggleAreaExpansion,

    // Selection state
    localSelectedTables,
    resetSelection,

    // Selection handlers
    handleTableToggle,
    handleAreaToggle,

    // Selection checkers
    isAreaSelected,
    isAreaIndeterminate,
  } = useTableSelector({
    selectedTables,
    storeId,
    brandId,
    enabled: open,
  })

  const handleSave = () => {
    onSave(Array.from(localSelectedTables))
    onOpenChange(false)
  }

  const handleCancel = () => {
    resetSelection()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle>Chọn bàn</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          {isLoading ? (
            <div className='text-center'>Đang tải danh sách bàn...</div>
          ) : areaData.length === 0 ? (
            <div className='text-center text-gray-500'>
              Không có bàn nào trong cửa hàng này
            </div>
          ) : (
            <div className='max-h-96 space-y-2 overflow-y-auto'>
              {areaData.map((area) => (
                <div key={area.area_uid} className='space-y-2'>
                  <div className='flex items-center gap-2 rounded-md bg-gray-100 p-2'>
                    <button
                      onClick={() => toggleAreaExpansion(area.area_uid)}
                      className='flex items-center gap-1 rounded p-1 hover:bg-gray-200'
                    >
                      {expandedAreas.has(area.area_uid) ? (
                        <IconChevronDown className='h-4 w-4' />
                      ) : (
                        <IconChevronUp className='h-4 w-4' />
                      )}
                    </button>
                    <Checkbox
                      checked={
                        isAreaSelected(area) ||
                        (isAreaIndeterminate(area) && 'indeterminate')
                      }
                      onCheckedChange={() => handleAreaToggle(area)}
                    />
                    <span className='font-medium'>{area.area_name}</span>
                    <span className='text-sm text-gray-500'>
                      ({area.tables.length} bàn)
                    </span>
                  </div>

                  {expandedAreas.has(area.area_uid) && (
                    <div className='ml-6 space-y-1'>
                      {area.tables.map((table) => (
                        <div
                          key={table.id}
                          className='flex items-center gap-2 rounded p-2 hover:bg-gray-50'
                        >
                          <Checkbox
                            checked={localSelectedTables.has(table.id)}
                            onCheckedChange={() => handleTableToggle(table.id)}
                          />
                          <span className='text-sm'>{table.table_name}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel}>
            Hủy
          </Button>
          <Button onClick={handleSave}>
            Lưu ({localSelectedTables.size} bàn)
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
