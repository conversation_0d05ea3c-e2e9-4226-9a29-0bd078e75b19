import { useMemo } from 'react'

import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui'

import { useDashboardContext } from '../../context'
import { StoresOverview } from '../stores-overview'

export function TopStoresChart() {
  const { defaultDateRange, storesData, isStoresLoading, storesError } = useDashboardContext()

  const formatDateRange = useMemo(() => {
    const formatDate = (date: Date) => {
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`
    }

    const fromFormatted = `${formatDate(defaultDateRange.from)} 00:00`
    const toFormatted = `${formatDate(defaultDateRange.to)} 23:59`

    return `Báo cáo tính từ ${fromFormatted} - ${toFormatted}`
  }, [defaultDateRange])

  if (isStoresLoading) {
    return (
      <Card className='col-span-1'>
        <CardHeader>
          <CardTitle>Top 5 cửa hàng có doanh thu cao</CardTitle>
          <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
          <CardDescription className='text-xs font-medium text-black'>Doanh thu (₫)</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='text-muted-foreground py-8 text-center'>Đang tải dữ liệu...</div>
        </CardContent>
      </Card>
    )
  }

  if (storesError) {
    return (
      <Card className='col-span-1'>
        <CardHeader>
          <CardTitle>Top 5 cửa hàng có doanh thu cao</CardTitle>
          <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
          <CardDescription className='text-xs font-medium text-black'>Doanh thu (₫)</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='py-8 text-center text-red-500'>Lỗi: {storesError}</div>
        </CardContent>
      </Card>
    )
  }

  if (!storesData || storesData.length === 0) {
    return (
      <Card className='col-span-1'>
        <CardHeader>
          <CardTitle>Top 5 cửa hàng có doanh thu cao</CardTitle>
          <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
          <CardDescription className='text-xs font-medium text-black'>Doanh thu (₫)</CardDescription>
          <CardAction>
            {/* <button className='cursor-pointer text-xs text-blue-600 hover:text-blue-800'>Chi tiết</button> */}
          </CardAction>
        </CardHeader>
        <CardContent>
          <div className='flex h-[350px] items-center justify-center'>
            <div className='text-muted-foreground text-sm'>Chưa có thông tin</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className='col-span-1'>
      <CardHeader>
        <CardTitle>Top 5 cửa hàng có doanh thu cao</CardTitle>
        <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
        <CardDescription className='text-xs font-medium text-black'>Doanh thu (₫)</CardDescription>
        <CardAction>
          {/* <button className='cursor-pointer text-xs text-blue-600 hover:text-blue-800'>Chi tiết</button> */}
        </CardAction>
      </CardHeader>
      <CardContent className='pl-2'>
        <StoresOverview storesData={storesData} />
      </CardContent>
    </Card>
  )
}
