import { ColumnDef } from '@tanstack/react-table'
import { IconTrash } from '@tabler/icons-react'
import { ItemCategory } from '@/types/item-categories'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

export const categoryColumns: ColumnDef<ItemCategory>[] = [
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => {
      const index = row.index + 1
      return <div className='w-[50px] font-medium'>{index}</div>
    },
    enableSorting: false,
  },
  {
    accessorKey: 'item_type_id',
    header: 'Mã nhóm',
    cell: ({ row }) => {
      const category = row.original
      return <span className='font-medium'>{category.item_type_id}</span>
    },
  },
  {
    accessorKey: 'item_type_name',
    header: 'Tên nhóm',
    cell: ({ row }) => {
      const category = row.original
      return <span className='font-medium'>{category.item_type_name}</span>
    },
  },
  {
    id: 'actions',
    header: 'Thao tác',
    cell: ({ row, table }) => {
      const category = row.original
      const isActive = category.active === 1
      const meta = table.options.meta as {
        onToggleCategoryStatus?: (category: ItemCategory) => void
      }

      return (
        <Badge
          variant={isActive ? 'default' : 'destructive'}
          className={`cursor-pointer ${
            isActive
              ? 'bg-green-500 text-white hover:bg-green-600'
              : 'bg-red-500 text-white hover:bg-red-600'
          }`}
          onClick={() => meta?.onToggleCategoryStatus?.(category)}
        >
          {isActive ? 'Active' : 'Deactive'}
        </Badge>
      )
    },
  },
  {
    id: 'delete',
    header: '',
    cell: ({ row, table }) => {
      const category = row.original
      const meta = table.options.meta as {
        onDeleteCategory?: (category: ItemCategory) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          onClick={() => meta?.onDeleteCategory?.(category)}
          className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
          title='Xóa nhóm món'
        >
          <IconTrash className='h-4 w-4' />
          <span className='sr-only'>Xóa nhóm {category.item_type_name}</span>
        </Button>
      )
    },
  },
]
