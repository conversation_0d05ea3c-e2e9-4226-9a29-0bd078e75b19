import React, { useState, useMemo } from 'react'

import { usePosCitiesData } from '@/hooks'
import { usePosStores } from '@/stores'
import { ChevronDown } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Combobox } from '@/components/pos/combobox'
import {
  Button,
  Checkbox,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Badge
} from '@/components/ui'

interface StoreSelectionDropdownProps {
  selectedCities: string[]
  selectedStores: string[]
  onCitiesChange: (cities: string[]) => void
  onStoresChange: (stores: string[]) => void
  className?: string
  excludeCities?: string[]
  excludeStores?: string[]
}

export const StoreSelectionDropdown: React.FC<StoreSelectionDropdownProps> = ({
  selectedCities,
  selectedStores,
  onCitiesChange,
  onStoresChange,
  className,
  excludeCities = [],
  excludeStores = []
}) => {
  const [open, setOpen] = useState(false)
  const [expandedCities, setExpandedCities] = useState<Set<string>>(new Set())
  const [storeTypeFilter, setStoreTypeFilter] = useState<'all' | 'chain' | 'franchise'>('all')
  const [searchTerm, _setSearchTerm] = useState('')

  const { cities } = usePosCitiesData()
  const { currentBrandStores } = usePosStores()

  const availableCities = useMemo(() => {
    return cities.filter(city => !excludeCities.includes((city as any).id || (city as any).city_id))
  }, [cities, excludeCities])

  const availableStores = useMemo(() => {
    return currentBrandStores
      .filter(store => !excludeStores.includes((store as any).id || (store as any).store_id))
      .filter(store => {
        if (storeTypeFilter === 'all') return true
        if (storeTypeFilter === 'chain') return (store as any).is_franchise === 0
        return (store as any).is_franchise === 1
      })
  }, [currentBrandStores, excludeStores, storeTypeFilter])

  const getStoresByCity = (cityId: string) => {
    return availableStores.filter(store => (store as any).city_uid === cityId)
  }

  const toggleCityExpansion = (cityId: string) => {
    const newExpanded = new Set(expandedCities)
    if (newExpanded.has(cityId)) {
      newExpanded.delete(cityId)
    } else {
      newExpanded.add(cityId)
    }
    setExpandedCities(newExpanded)
  }

  const handleCityToggle = (cityId: string, checked: boolean) => {
    const cityStores = getStoresByCity(cityId)
    const cityStoreIds = cityStores.map(store => (store as any).id || (store as any).store_id)

    if (checked) {
      onCitiesChange([...selectedCities, cityId])
      const current = selectedStores.filter(id => id !== 'all-stores')
      const merged = Array.from(new Set([...current, ...cityStoreIds]))
      onStoresChange(merged)
    } else {
      onCitiesChange(selectedCities.filter(id => id !== cityId))
      const remaining = selectedStores.filter(storeId => !cityStoreIds.includes(storeId))
      onStoresChange(remaining.length > 0 ? remaining : ['all-stores'])
    }
  }

  const handleStoreToggle = (storeId: string, checked: boolean) => {
    const store = currentBrandStores.find(s => ((s as any).id || (s as any).store_id) === storeId)
    const cityId = (store as any)?.city_uid

    if (checked) {
      const newSelectedStores = [...selectedStores, storeId]
      onStoresChange(newSelectedStores)

      if (cityId) {
        const cityStores = getStoresByCity(cityId)
        const cityStoreIds = cityStores.map(s => (s as any).id || (s as any).store_id)
        const allCityStoresSelected = cityStoreIds.every(
          id => newSelectedStores.includes(id) || selectedCities.includes(cityId)
        )

        if (allCityStoresSelected) {
          const storesWithoutCity = newSelectedStores.filter(id => !cityStoreIds.includes(id))
          onStoresChange(storesWithoutCity)
          onCitiesChange([...selectedCities, cityId])
        }
      }
    } else {
      if (cityId && selectedCities.includes(cityId)) {
        const cityStores = getStoresByCity(cityId)
        const cityStoreIds = cityStores.map(s => (s as any).id || (s as any).store_id)
        const otherCityStores = cityStoreIds.filter(id => id !== storeId)

        onCitiesChange(selectedCities.filter(id => id !== cityId))
        onStoresChange([...selectedStores, ...otherCityStores])
      } else {
        onStoresChange(selectedStores.filter(id => id !== storeId))
      }
    }
  }

  const isCitySelected = (cityId: string): boolean => {
    return selectedCities.includes(cityId)
  }

  const isCityPartiallySelected = (cityId: string): boolean => {
    const cityStores = getStoresByCity(cityId)
    const cityStoreIds = cityStores.map(store => (store as any).id || (store as any).store_id)
    const selectedCityStores = selectedStores.filter(storeId => cityStoreIds.includes(storeId))
    return selectedCityStores.length > 0 && selectedCityStores.length < cityStoreIds.length
  }

  const isStoreSelected = (storeId: string): boolean => {
    return selectedStores.includes(storeId)
  }

  const selectedCount = useMemo(() => {
    const cityStoreCount = selectedCities.reduce((count, cityId) => {
      const cityStores = getStoresByCity(cityId)
      return count + cityStores.length
    }, 0)

    const actualSelectedStores = selectedStores.filter(id => id !== 'all-stores')

    const individualStoreCount = actualSelectedStores.filter(storeId => {
      const store = currentBrandStores.find((s: any) => ((s as any).id || (s as any).store_id) === storeId)
      if (!store) return false

      const cityId = (store as any).city_uid
      return !selectedCities.includes(cityId)
    }).length

    return cityStoreCount + individualStoreCount
  }, [selectedCities, selectedStores, currentBrandStores])

  const displayText = selectedCount > 0 ? `Đã chọn ${selectedCount} cửa hàng` : 'Tất cả cửa hàng'
  const hasData = useMemo(() => {
    const needle = searchTerm.trim().toLowerCase()
    return availableCities.some(city => {
      const cityId = (city as any).id || (city as any).city_id
      const stores = getStoresByCity(cityId)
      if (!needle) return stores.length > 0
      return stores.some(s =>
        String((s as any).store_name || '')
          .toLowerCase()
          .includes(needle)
      )
    })
  }, [availableCities, availableStores, searchTerm])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className={cn('w-full justify-between', className)}
        >
          <span className='truncate'>{displayText}</span>
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-80 p-0' align='start'>
        <div className='max-h-96 overflow-y-auto'>
          <div className='p-2'>
            <div className='mb-2 flex flex-col items-start justify-center gap-2'>
              <Combobox
                options={[
                  { value: 'all', label: 'Tất cả cửa hàng' },
                  { value: 'chain', label: 'Chuỗi' },
                  { value: 'franchise', label: 'Nhượng quyền' }
                ]}
                value={storeTypeFilter}
                onValueChange={v => setStoreTypeFilter(!v ? 'all' : (v as 'all' | 'chain' | 'franchise'))}
                placeholder='Loại cửa hàng'
                searchPlaceholder='Tìm kiếm...'
                className='w-full'
              />
            </div>

            {!hasData && (
              <div className='text-muted-foreground flex h-40 items-center justify-center text-lg font-semibold'>
                Không có dữ liệu
              </div>
            )}

            {hasData &&
              availableCities
                .filter(city => {
                  const cityId = (city as any).id || (city as any).city_id
                  const cityStores = getStoresByCity(cityId)
                  const needle = searchTerm.trim().toLowerCase()
                  if (!needle) return cityStores.length > 0
                  return cityStores.some(s =>
                    String((s as any).store_name || '')
                      .toLowerCase()
                      .includes(needle)
                  )
                })
                .map(city => {
                  const cityId = (city as any).id || (city as any).city_id
                  const needle = searchTerm.trim().toLowerCase()
                  const allCityStores = getStoresByCity(cityId)
                  const cityStores = needle
                    ? allCityStores.filter(s =>
                        String((s as any).store_name || '')
                          .toLowerCase()
                          .includes(needle)
                      )
                    : allCityStores
                  const isExpanded = needle ? true : expandedCities.has(cityId)
                  const isCityChecked = isCitySelected(cityId)
                  const isPartiallySelected = isCityPartiallySelected(cityId)

                  return (
                    <div key={cityId} className='mb-2'>
                      <Collapsible open={isExpanded} onOpenChange={() => toggleCityExpansion(cityId)}>
                        <div className='flex items-center space-x-2 rounded p-2 hover:bg-gray-50'>
                          <Checkbox
                            checked={isCityChecked}
                            ref={el => {
                              if (el && isPartiallySelected && !isCityChecked) {
                                const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement
                                if (checkbox) {
                                  checkbox.indeterminate = true
                                }
                              }
                            }}
                            onCheckedChange={checked => handleCityToggle(cityId, checked as boolean)}
                          />
                          <CollapsibleTrigger className='flex flex-1 items-center space-x-2 text-left'>
                            <span className='font-medium'>{(city as any).city_name}</span>
                            <span className='text-muted-foreground text-sm'>({cityStores.length} cửa hàng)</span>
                            <ChevronDown
                              className={cn(
                                'ml-auto h-4 w-4 transition-transform',
                                isExpanded && 'rotate-180 transform'
                              )}
                            />
                          </CollapsibleTrigger>
                        </div>

                        <CollapsibleContent>
                          {isExpanded && (
                            <div className='ml-6 space-y-1'>
                              {cityStores.map(store => {
                                const storeId = (store as any).id || (store as any).store_id
                                const isChecked = isStoreSelected(storeId) || isCityChecked

                                return (
                                  <div
                                    key={storeId}
                                    className='flex items-center space-x-2 rounded p-2 hover:bg-gray-50'
                                  >
                                    <Checkbox
                                      checked={isChecked}
                                      onCheckedChange={checked => handleStoreToggle(storeId, checked as boolean)}
                                    />
                                    <span className='text-sm'>{(store as any).store_name}</span>
                                    {typeof (store as any).is_franchise !== 'undefined' &&
                                      ((store as any).is_franchise === 1 ? (
                                        <Badge className='ml-2'>Nhượng quyền</Badge>
                                      ) : (
                                        <Badge className='ml-2'>Chuỗi</Badge>
                                      ))}
                                  </div>
                                )
                              })}
                            </div>
                          )}
                        </CollapsibleContent>
                      </Collapsible>
                    </div>
                  )
                })}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
