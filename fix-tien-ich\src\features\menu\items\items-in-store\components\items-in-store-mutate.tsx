import { useEffect, useState } from 'react'

import { z } from 'zod'

import { type SubmitHandler, useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { X } from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'

import { useStoresData, useItemClassesData, useItemTypesData, useUnitsData } from '@/hooks/api'
import { useImageUpload } from '@/hooks/api/use-images'

import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'

import type { ItemsInCity } from '../../items-in-city/data'
import { ItemFormSections, PriceSourceDialog } from '../../items-in-city/detail'
import { ItemsInStore } from '../data'
import {
  useCreateItemInStore,
  useItemByListId,
  useItemInStoreDetail,
  useUpdateItemInStore,
  useUpdateItemInStoreStatus
} from '../hooks'
import type { CreateItemInStoreRequest, UpdateItemInStoreRequest } from '../hooks/items-in-store-api'

const formSchema = z.object({
  item_name: z.string().min(1, 'Tên món là bắt buộc'),
  ots_price: z.coerce.number().min(0, 'Giá phải lớn hơn hoặc bằng 0'),
  ta_price: z.coerce.number().min(0, 'Giá phải lớn hơn hoặc bằng 0'),
  description: z.string().optional(),
  item_id_barcode: z
    .string()
    .optional()
    .refine(
      val => !val || /^[a-zA-Z0-9]*$/.test(val),
      'Mã barcode chỉ được chứa chữ cái và số, không có dấu cách hoặc ký tự đặc biệt'
    ),
  item_id_mapping: z.string().optional(),
  is_eat_with: z.preprocess(val => Boolean(val), z.boolean()),
  is_featured: z.preprocess(val => Boolean(val), z.boolean()),
  item_class_uid: z.string().optional(),
  item_type_uid: z.string().min(1, 'Vui lòng chọn loại món'),
  store_uid: z.string().min(1, 'Vui lòng chọn cửa hàng'),
  sku: z.string().max(50, 'SKU không được vượt quá 50 ký tự').optional(),
  item_id: z.string().optional(),
  unit_uid: z.string().min(1, 'Vui lòng chọn đơn vị tính'),
  unit_secondary_uid: z.string().optional(),
  ots_tax: z.coerce.number().min(0).max(100),
  ta_tax: z.coerce.number().min(0).max(100),
  time_cooking: z.coerce.number().min(0),
  enable_edit_price: z.preprocess(val => Boolean(val), z.boolean()),
  is_print_label: z.preprocess(val => Boolean(val), z.boolean()),
  is_allow_discount: z.preprocess(val => Boolean(val), z.boolean()),
  is_virtual_item: z.preprocess(val => Boolean(val), z.boolean()),
  is_item_service: z.preprocess(val => Boolean(val), z.boolean()),
  is_buffet_item: z.preprocess(val => Boolean(val), z.boolean()),
  inqrFormula: z.string().optional(),
  is_service: z.preprocess(val => Boolean(val), z.boolean()),
  priceBySource: z.preprocess(val => Boolean(val), z.boolean()),
  selectedDays: z.array(z.string()),
  selectedHours: z.array(z.string()),
  sort: z.coerce.number().min(0).default(0),
  sort_online: z.coerce.number().min(0).default(1000),
  customization_uid: z.string().optional(),

  // Additional fields from items-in-city
  buffet_config: z
    .array(
      z.object({
        quantity: z.number(),
        price: z.number()
      })
    )
    .default([]),
  time_sale_date_week: z.coerce.number().default(0),
  time_sale_hour_day: z.coerce.number().default(0),

  allow_take_away: z.coerce.number().min(0).max(1).default(1),
  list_order: z.coerce.number().min(0).default(0),
  is_material: z.coerce.number().min(0).max(1).default(0),
  quantity_default: z.coerce.number().min(0).default(0),
  price_change: z.coerce.number().min(0).default(0),
  point: z.coerce.number().min(0).default(0),
  is_gift: z.coerce.number().min(0).max(1).default(0),
  is_fc: z.coerce.number().min(0).max(1).default(0),
  show_on_web: z.coerce.number().min(0).max(1).default(0),
  show_price_on_web: z.coerce.number().min(0).max(1).default(0),
  cost_price: z.coerce.number().min(0).default(0),
  quantity_limit: z.coerce.number().min(0).default(0),
  is_kit: z.coerce.number().min(0).max(1).default(0),
  process_index: z.coerce.number().min(0).default(0),
  quantity_per_day: z.coerce.number().min(0).default(0),
  is_parent: z.coerce.number().min(0).max(1).default(0),
  is_sub: z.coerce.number().min(0).max(1).default(0),
  effective_date: z.coerce.number().min(0).default(0),
  expire_date: z.coerce.number().min(0).default(0)
})

export type FormValues = z.infer<typeof formSchema>

// Helper function to convert ItemsInStore to ItemsInCity format for ItemConfiguration component
const convertItemsInStoreToItemsInCity = (item: ItemsInStore): ItemsInCity => {
  // Extract store information
  const storeData = (item as any).Stores?.[0] || {}
  const storeName = storeData.store_name || ''

  return {
    id: item.id,
    code: item.item_id,
    name: item.item_name,
    price: item.ots_price,
    vatPercent: item.ots_tax,
    categoryGroup: '', // Will be populated by form data
    itemType: '', // Will be populated by form data
    itemClass: '', // Will be populated by form data
    unit: '', // Will be populated by form data
    sideItems: item.item_id_eat_with || undefined,
    city: '', // Will be populated by form data
    buffetConfig: item.extra_data?.is_buffet_item ? 'Đã cấu hình' : 'Chưa cấu hình',
    customization: item.customization_uid || undefined,
    isActive: Boolean(item.active),
    createdAt: new Date(
      typeof item.created_at === 'number' ? item.created_at * 1000 : new Date(item.created_at).getTime()
    ),
    originalData: {
      item_id: item.item_id,
      item_code: item.item_id,
      item_name: item.item_name,
      price: item.ots_price,
      vat_percent: item.ots_tax,
      category_group: '', // Will be populated by form data
      item_type: '', // Will be populated by form data
      unit: '', // Will be populated by form data
      side_items: item.item_id_eat_with || '',
      city_name: '', // Will be populated by form data
      buffet_config: item.extra_data?.is_buffet_item ? 'Đã cấu hình' : 'Chưa cấu hình',
      customization_uid: item.customization_uid || '',
      active: item.active ? 1 : 0,
      created_at: typeof item.created_at === 'number' ? item.created_at : 0,
      store_uid: item.store_uid || '',
      store_name: storeName,
      // Cast to any to include additional fields not in the original type
      ...(item as any)
    } as any
  }
}

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: ItemsInStore
  isCopyMode?: boolean
}

export function ItemsInStoreMutate({ open, onOpenChange, currentRow, isCopyMode = false }: Props) {
  const isUpdate = !!currentRow && !isCopyMode
  const isCopy = !!currentRow && isCopyMode
  const [openDialog, setOpenDialog] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null)
  const [priceSources, setPriceSources] = useState<Array<{ source: string; sourceName: string; amount: number }>>([])

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  // Get data for copy mode
  const copyFromId = isCopy ? currentRow?.id : undefined
  const itemId = isUpdate ? currentRow?.id : undefined
  const targetId = itemId || copyFromId

  const { data: currentRowDetail } = useItemInStoreDetail(targetId, !!(targetId && (isUpdate || isCopy)))
  const { data: itemByListId } = useItemByListId(
    { list_id: currentRowDetail?.item_id || '' },
    !!(currentRowDetail?.item_id && (isUpdate || isCopy))
  )

  const { data: stores = [] } = useStoresData()
  const { data: itemTypes = [] } = useItemTypesData({ skip_limit: true })
  const { data: units = [] } = useUnitsData()
  const { data: itemClasses = [] } = useItemClassesData({ skip_limit: true })

  const createItemInStoreMutation = useCreateItemInStore()
  const updateItemInStoreMutation = useUpdateItemInStore()
  const updateStatusMutation = useUpdateItemInStoreStatus()
  const { mutate: uploadImage } = useImageUpload()

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      item_name: '',
      ots_price: 0,
      ta_price: 0,
      description: '',
      item_id_barcode: '',
      item_id_mapping: '',
      is_eat_with: false,
      is_featured: false,
      item_class_uid: '',
      item_type_uid: '',
      store_uid: '',
      sku: '',
      item_id: '',
      unit_uid: '',
      unit_secondary_uid: '',
      ots_tax: 0,
      ta_tax: 0,
      time_cooking: 0,
      enable_edit_price: false,
      is_print_label: false,
      is_allow_discount: false,
      is_virtual_item: false,
      is_item_service: false,
      is_buffet_item: false,
      inqrFormula: '',
      is_service: false,
      priceBySource: false,
      selectedDays: [],
      selectedHours: [],
      buffet_config: [],
      time_sale_date_week: 0,
      time_sale_hour_day: 0,
      sort: 0,
      sort_online: 1000,
      customization_uid: '',
      allow_take_away: 1,
      list_order: 0,
      is_material: 0,
      quantity_default: 0,
      price_change: 0,
      point: 0,
      is_gift: 0,
      is_fc: 0,
      show_on_web: 0,
      show_price_on_web: 0,
      cost_price: 0,
      quantity_limit: 0,
      is_kit: 0,
      process_index: 0,
      quantity_per_day: 0,
      is_parent: 0,
      is_sub: 0,
      effective_date: 0,
      expire_date: 0
    }
  })

  // Reset image state when modal opens/closes
  useEffect(() => {
    if (!open) {
      setImagePreview(null)
      setSelectedImageFile(null)
    }
  }, [open])

  useEffect(() => {
    if (open) {
      if ((isUpdate || isCopy) && currentRow) {
        // Set existing image if available
        if (currentRow.image_path) {
          setImagePreview(currentRow.image_path)
        }

        const resetData = currentRowDetail || itemByListId || currentRow

        form.reset({
          item_name: isCopy ? `${resetData.item_name} (Copy)` : resetData.item_name || '',
          ots_price: resetData.ots_price || 0,
          ta_price: resetData.ta_price || resetData.ots_price || 0,
          description: resetData.description || '',
          item_id_barcode: resetData.item_id_barcode || '',
          item_id_mapping: resetData.item_id_mapping || '',
          is_eat_with: Boolean(resetData.is_eat_with),
          is_featured: Boolean(resetData.extra_data?.no_update_quantity_toping),
          item_class_uid: resetData.item_class_uid || '',
          item_type_uid: resetData.item_type_uid || '',
          store_uid: resetData.store_uid || '',
          sku: (resetData as any).sku || '',
          item_id: isCopy ? '' : resetData.item_id || '',
          unit_uid: resetData.unit_uid || '',
          unit_secondary_uid: resetData.unit_secondary_uid || '',
          ots_tax: resetData.ots_tax || 0,
          ta_tax: resetData.ta_tax || resetData.ots_tax || 0,
          time_cooking: resetData.time_cooking || 0,
          enable_edit_price: Boolean(resetData.extra_data?.enable_edit_price),
          is_print_label: Boolean(resetData.is_print_label),
          is_allow_discount: Boolean(resetData.is_allow_discount),
          is_virtual_item: Boolean(resetData.extra_data?.is_virtual_item),
          is_item_service: Boolean(resetData.extra_data?.is_item_service),
          is_buffet_item: Boolean(resetData.extra_data?.is_buffet_item),
          inqrFormula: (resetData as any).inqrFormula || '',
          is_service: Boolean(resetData.is_service),
          priceBySource: Boolean((resetData as any).priceBySource),
          selectedDays: (resetData as any).selectedDays || [],
          selectedHours: (resetData as any).selectedHours || [],
          buffet_config: (resetData as any).buffet_config || [],
          time_sale_date_week: resetData.time_sale_date_week || 0,
          time_sale_hour_day: resetData.time_sale_hour_day || 0,
          sort: resetData.sort || 0,
          sort_online: (resetData as any).sort_online || 1000,
          customization_uid: resetData.customization_uid || '',
          allow_take_away: resetData.allow_take_away || 1,
          list_order: resetData.list_order || 0,
          is_material: resetData.is_material || 0,
          quantity_default: resetData.quantity_default || 0,
          price_change: resetData.price_change || 0,
          point: resetData.point || 0,
          is_gift: resetData.is_gift || 0,
          is_fc: resetData.is_fc || 0,
          show_on_web: resetData.show_on_web || 0,
          show_price_on_web: resetData.show_price_on_web || 0,
          cost_price: resetData.cost_price || 0,
          quantity_limit: resetData.quantity_limit || 0,
          is_kit: resetData.is_kit || 0,
          process_index: resetData.process_index || 0,
          quantity_per_day: resetData.quantity_per_day || 0,
          is_parent: resetData.is_parent || 0,
          is_sub: resetData.is_sub || 0,
          effective_date: resetData.effective_date || 0,
          expire_date: resetData.expire_date || 0
        })
      } else {
        // Reset to default values for create mode
        form.reset()
      }
    }
  }, [isUpdate, isCopy, currentRow, currentRowDetail, itemByListId, open, form])

  const onSubmit: SubmitHandler<FormValues> = async data => {
    try {
      if (!company?.id || !selectedBrand?.id) {
        return
      }

      // Upload image first if there's a selected file
      let imageUrl = ''
      if (selectedImageFile) {
        try {
          const uploadResponse = await new Promise<{ data: { image_url: string } }>((resolve, reject) => {
            uploadImage(selectedImageFile, {
              onSuccess: data => resolve(data),
              onError: error => reject(error)
            })
          })
          imageUrl = uploadResponse.data.image_url
        } catch (error) {
          console.error('Image upload failed:', error)
          // Continue without image if upload fails
        }
      }

      const requestData: any = {
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        item_name: data.item_name,
        ots_price: data.ots_price,
        ta_price: data.ta_price || data.ots_price,
        description: data.description || '',
        item_id_barcode: data.item_id_barcode || '',
        item_id_mapping: data.item_id_mapping || '',
        is_eat_with: data.is_eat_with ? 1 : 0,
        is_featured: data.is_featured ? 1 : 0,
        item_class_uid: data.item_class_uid || null,
        item_type_uid: data.item_type_uid,
        store_uid: data.store_uid,
        city_uid: (() => {
          // If copying, try to get city_uid from original item
          if (isCopy && currentRowDetail) {
            const originalCityUid = (currentRowDetail as any)?.city_uid
            const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

            if (originalCityUid && guidRegex.test(originalCityUid)) {
              console.log('🔍 Using original city_uid from copy:', originalCityUid)
              return originalCityUid
            }
          }

          // Get city_uid from selected store
          const selectedStore = stores.find(store => store.id === data.store_uid)
          const cityId = selectedStore?.cityId

          // Validate GUID format
          const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

          console.log('🔍 City UID Debug:', {
            selectedStoreId: data.store_uid,
            selectedStore: selectedStore,
            cityId: cityId,
            isValidGuid: cityId ? guidRegex.test(cityId) : false
          })

          if (cityId && guidRegex.test(cityId)) {
            return cityId
          }

          // Fallback from copy.md
          return 'ce8ca87c-e4b9-402a-8acc-3136f6bcf42d'
        })(),
        sku: data.sku || '',
        item_id: data.item_id || `ITEM-${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
        unit_uid: data.unit_uid,
        unit_secondary_uid: data.unit_secondary_uid || null,
        ots_tax: data.ots_tax,
        ta_tax: data.ta_tax || data.ots_tax,
        time_cooking: data.time_cooking,
        enable_edit_price: data.enable_edit_price ? 1 : 0,
        is_print_label: data.is_print_label ? 1 : 0,
        is_allow_discount: data.is_allow_discount ? 1 : 0,
        is_virtual_item: data.is_virtual_item ? 1 : 0,
        is_item_service: data.is_item_service ? 1 : 0,
        is_buffet_item: data.is_buffet_item ? 1 : 0,
        is_service: data.is_service ? 1 : 0,
        sort: data.sort,
        sort_online: data.sort_online,
        customization_uid: data.customization_uid || '',
        time_sale_date_week: data.time_sale_date_week,
        time_sale_hour_day: data.time_sale_hour_day,
        allow_take_away: data.allow_take_away,
        list_order: data.list_order,
        is_material: data.is_material,
        quantity_default: data.quantity_default,
        price_change: data.price_change,
        point: data.point,
        is_gift: data.is_gift,
        is_fc: data.is_fc,
        show_on_web: data.show_on_web,
        show_price_on_web: data.show_price_on_web,
        cost_price: data.cost_price,
        quantity_limit: data.quantity_limit,
        is_kit: data.is_kit,
        process_index: data.process_index,
        quantity_per_day: data.quantity_per_day,
        is_parent: data.is_parent,
        is_sub: data.is_sub,
        effective_date: data.effective_date,
        expire_date: data.expire_date,
        image_path: imageUrl || (isUpdate ? currentRow?.image_path || '' : ''),
        image_path_thumb: imageUrl || (isUpdate ? currentRow?.image_path_thumb || '' : '')
      }

      if (isUpdate && currentRow?.id) {
        updateItemInStoreMutation.mutate({
          item_uid: currentRow.id,
          ...requestData
        } as UpdateItemInStoreRequest)
      } else {
        createItemInStoreMutation.mutate(requestData as CreateItemInStoreRequest)
      }

      onOpenChange(false)
    } catch (error) {
      console.error('Error submitting form:', error)
    }
  }

  const handleImageUpload = (file: File) => {
    setSelectedImageFile(file)
    const reader = new FileReader()
    reader.onload = e => {
      setImagePreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleImageRemove = () => {
    setImagePreview(null)
    setSelectedImageFile(null)
  }

  const handlePriceSourceSave = (data: { source: string; amount: number }) => {
    const currentSources = priceSources
    const newSource = {
      source: data.source,
      sourceName: data.source,
      amount: data.amount
    }
    setPriceSources([...currentSources, newSource])
    setOpenDialog(false)
  }

  const handleClose = () => {
    onOpenChange(false)
  }

  const handleDeactive = async () => {
    if (!currentRow?.id) return

    try {
      await updateStatusMutation.mutateAsync({
        item_uid: currentRow.id,
        active: false
      })
      onOpenChange(false)
    } catch (error) {}
  }

  if (!open) return null

  return (
    <>
      <div className='container mx-auto px-4 py-8'>
        <div className='mb-8'>
          <div className='mb-4 flex items-center justify-between'>
            <Button variant='ghost' size='sm' onClick={handleClose} className='flex items-center'>
              <X className='h-4 w-4' />
            </Button>
            <div className='flex gap-2'>
              {isUpdate && currentRow?.active && (
                <Button
                  type='button'
                  variant='outline'
                  className='border-red-500 text-red-500 hover:bg-red-50'
                  disabled={updateStatusMutation.isPending}
                  onClick={handleDeactive}
                >
                  {updateStatusMutation.isPending ? 'Đang deactive...' : 'Deactive'}
                </Button>
              )}

              <Button
                type='button'
                disabled={createItemInStoreMutation.isPending || updateItemInStoreMutation.isPending}
                onClick={async () => {
                  const isValid = await form.trigger()

                  if (isValid) {
                    form.handleSubmit(onSubmit)()
                  }
                }}
              >
                {createItemInStoreMutation.isPending || updateItemInStoreMutation.isPending
                  ? 'Đang lưu và đồng bộ...'
                  : 'Lưu và đồng bộ'}
              </Button>
              <Button
                type='button'
                disabled={createItemInStoreMutation.isPending || updateItemInStoreMutation.isPending}
                onClick={async () => {
                  const isValid = await form.trigger()

                  if (isValid) {
                    form.handleSubmit(onSubmit)()
                  }
                }}
              >
                {createItemInStoreMutation.isPending || updateItemInStoreMutation.isPending ? 'Đang lưu...' : 'Lưu'}
              </Button>
            </div>
          </div>

          <div className='text-center'>
            <h1 className='mb-2 text-3xl font-bold'>
              {isUpdate ? 'Chi tiết món' : isCopyMode ? 'Sao chép món' : 'Tạo món'}
            </h1>
          </div>
        </div>

        <div className='mx-auto max-w-4xl'>
          <div className='rounded-lg border bg-white p-6 shadow-sm'>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
                <ItemFormSections
                  form={form}
                  itemTypes={itemTypes}
                  itemClasses={itemClasses}
                  units={units}
                  cities={stores as any}
                  imagePreview={imagePreview}
                  onImageChange={(event: any) => {
                    const file = event.target.files?.[0]
                    if (file) handleImageUpload(file)
                  }}
                  onImageRemove={handleImageRemove}
                  currentRow={currentRow ? convertItemsInStoreToItemsInCity(currentRow) : undefined}
                />
              </form>
            </Form>
          </div>
        </div>
      </div>

      {/* PriceSourceDialog rendered at root level to prevent layout issues */}
      <PriceSourceDialog
        open={openDialog}
        onOpenChange={setOpenDialog}
        cityUid={form.watch('store_uid') || ''}
        onConfirm={handlePriceSourceSave}
      />
    </>
  )
}
