import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { IconPlus } from '@tabler/icons-react'

import { ItemClass } from '@/types/item-class'
import { toast } from 'sonner'

import { getErrorMessage } from '@/utils/error-utils'

import { useItemClassesData, useUpdateItemClass, useDeleteItemClass } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ConfirmModal } from '@/components/pos'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { ActionBar, itemClassColumns, ItemClassDataTable } from './components'

export default function ListItemClassesPage() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [selectedItemClass, setSelectedItemClass] = useState<ItemClass | null>(null)

  const updateItemClassMutation = useUpdateItemClass()
  const deleteItemClassMutation = useDeleteItemClass()

  const {
    data: itemClasses,
    isLoading: itemClassesLoading,
    error: itemClassesError
  } = useItemClassesData({
    searchTerm: searchTerm || undefined
  })

  const isLoading = itemClassesLoading
  const error = itemClassesError

  const handleEditItemClass = (itemClass: ItemClass) => {
    // TODO: Implement edit functionality
    toast.info(`Chỉnh sửa loại món: ${itemClass.item_class_name}`)
  }

  const handleToggleItemClassStatus = async (itemClass: ItemClass) => {
    try {
      const updatedItemClass = {
        ...itemClass,
        active: itemClass.active === 1 ? 0 : 1
      }

      await updateItemClassMutation.mutateAsync(updatedItemClass)

      const statusText = updatedItemClass.active === 1 ? 'kích hoạt' : 'vô hiệu hóa'
      toast.success(`Đã ${statusText} loại món "${itemClass.item_class_name}"`)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleDeleteItemClass = (itemClass: ItemClass) => {
    setSelectedItemClass(itemClass)
    setConfirmModalOpen(true)
  }

  const handleConfirmDelete = async () => {
    if (!selectedItemClass) return

    try {
      await deleteItemClassMutation.mutateAsync(selectedItemClass.id)
      toast.success(`Loại món "${selectedItemClass.item_class_name}" đã được xóa thành công!`)
      setConfirmModalOpen(false)
      setSelectedItemClass(null)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleCreateItemClass = () => {
    navigate({ to: '/menu/item-class/detail' })
  }

  const handleRowClick = (itemClass: ItemClass) => {
    navigate({ to: '/menu/item-class/detail/$id', params: { id: itemClass.id } })
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <ActionBar
            title='Danh sách loại món'
            searchValue={searchQuery}
            searchPlaceholder='Tìm kiếm loại món...'
            onSearchChange={setSearchQuery}
            onSearchKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault()
                setSearchTerm(searchQuery)
              }
            }}
            actionButton={{
              label: 'Tạo loại món',
              icon: <IconPlus className='h-4 w-4' />,
              onClick: handleCreateItemClass
            }}
          />

          {error && (
            <div className='py-8 text-center'>
              <p className='text-red-600'>{getErrorMessage(error)}</p>
            </div>
          )}

          {!error && isLoading && (
            <div className='py-8 text-center'>
              <p>Đang tải dữ liệu loại món...</p>
            </div>
          )}

          {!error && !isLoading && (
            <ItemClassDataTable
              columns={itemClassColumns}
              data={itemClasses || []}
              onEditItemClass={handleEditItemClass}
              onDeleteItemClass={handleDeleteItemClass}
              onToggleItemClassStatus={handleToggleItemClassStatus}
              onRowClick={handleRowClick}
            />
          )}

          <ConfirmModal
            open={confirmModalOpen}
            onOpenChange={setConfirmModalOpen}
            content='Bạn có muốn xoá ?'
            onConfirm={handleConfirmDelete}
            isLoading={deleteItemClassMutation.isPending}
          />
        </div>
      </Main>
    </>
  )
}
