import { CMS_PERMISSIONS, MANAGER_PERMISSIONS, POS_PDA_PERMISSIONS } from '@/constants'

export const getAllPermissions = (): string[] => {
  const allPermissions: string[] = []

  allPermissions.push('POS_CMS', 'POS_CLIENT', 'POS_MANAGER')

  CMS_PERMISSIONS.forEach(section => {
    section.items.forEach(item => {
      if (item.permissions) {
        item.permissions.forEach(permission => {
          allPermissions.push(permission)
        })
      }
    })
  })

  POS_PDA_PERMISSIONS.forEach(section => {
    section.items.forEach(item => {
      if (item.permissions) {
        item.permissions.forEach(permission => {
          allPermissions.push(permission)
        })
      }
    })
  })

  MANAGER_PERMISSIONS.forEach(section => {
    section.items.forEach(item => {
      if (item.permissions) {
        item.permissions.forEach(permission => {
          allPermissions.push(permission)
        })
      }
    })
  })

  return allPermissions
}
