import React, { useState, useEffect } from 'react'
import { Device } from '@/types'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { PosModal } from '@/components/pos'

interface DeviceCopyModalProps {
  device: Device | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onCopy: (deviceName: string) => Promise<void>
}

const getDeviceTypeLabel = (type: string) => {
  const typeLabels: Record<string, string> = {
    POS: 'POS',
    POS_MINI: 'POS MINI',
    PDA: 'PDA',
  }
  return typeLabels[type] || type
}

export const DeviceCopyModal: React.FC<DeviceCopyModalProps> = ({
  device,
  open,
  onOpenChange,
  onCopy,
}) => {
  const [deviceName, setDeviceName] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (device && open) {
      setDeviceName('')
    }
  }, [device, open])

  const handleConfirm = async () => {
    if (!deviceName.trim()) return

    setIsLoading(true)
    try {
      await onCopy(deviceName.trim())
      onOpenChange(false)
      setDeviceName('')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    setDeviceName('')
  }

  if (!device) return null

  const title = `Sao chép thiết bị ${getDeviceTypeLabel(device.type)} ${device.name}`

  return (
    <PosModal
      title={title}
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      isLoading={isLoading}
      confirmDisabled={!deviceName.trim()}
    >
      <div className='flex items-center gap-4'>
        <Label
          htmlFor='device-name'
          className='min-w-[100px] text-sm font-medium'
        >
          Tên thiết bị
        </Label>
        <Input
          id='device-name'
          value={deviceName}
          onChange={(e) => setDeviceName(e.target.value)}
          className='flex-1'
          autoFocus
          required
          placeholder='Nhập tên thiết bị'
        />
      </div>
    </PosModal>
  )
}
