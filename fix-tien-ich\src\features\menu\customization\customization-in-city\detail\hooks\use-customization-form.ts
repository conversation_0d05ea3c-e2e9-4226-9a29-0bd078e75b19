import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { ExistingCustomization } from '@/types/customizations'
import { toast } from 'sonner'

import { useCreateCustomization, useUpdateCustomization } from '@/hooks/api'

interface UseCustomizationFormOptions {
  onSuccess?: () => void
  isEdit?: boolean
  customizationId?: string
}

interface CustomizationGroup {
  id: string
  name: string
  minRequired: number
  maxAllowed: number
  items: Array<{
    id: string
    name: string
    price: number
    code?: string
    size?: string
  }>
}

export function useCustomizationForm(options: UseCustomizationFormOptions = {}) {
  const navigate = useNavigate()
  const [customizationName, setCustomizationName] = useState('')
  const [selectedCityId, setSelectedCityId] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [existingCustomization, setExistingCustomization] = useState<ExistingCustomization | null>(null)

  const createCustomizationMutation = useCreateCustomization()
  const updateCustomizationMutation = useUpdateCustomization()

  const handleBack = () => {
    navigate({ to: '/menu/customization/customization-in-city' })
  }

  const handleSave = async (
    customizationGroups: CustomizationGroup[],
    selectedDishes: Set<string>,
    items: Array<{ id: string; item_id: string }>
  ) => {
    if (!customizationName.trim()) {
      toast.error('Vui lòng nhập tên customization')
      return
    }

    if (!selectedCityId) {
      toast.error('Vui lòng chọn thành phố')
      return
    }

    if (customizationGroups.length === 0) {
      toast.error('Vui lòng tạo ít nhất một nhóm')
      return
    }

    if (selectedDishes.size === 0) {
      toast.error('Vui lòng chọn ít nhất một món áp dụng')
      return
    }

    setIsSubmitting(true)
    try {
      const lstItemOptions = customizationGroups.map(group => ({
        LstItem_Id: group.items.map(item => item.code || item.id),
        Min_Permitted: group.minRequired,
        Max_Permitted: group.maxAllowed,
        Name: group.name,
        id: `CUS_GROUP_${Math.random().toString(36).substring(2, 7).toUpperCase()}`
      }))

      const selectedDishCodes = Array.from(selectedDishes).map(dishId => {
        const originalItem = items.find(apiItem => apiItem.id === dishId)
        return originalItem?.item_id || dishId
      })

      if (options.isEdit && options.customizationId) {
        await updateCustomizationMutation.mutateAsync({
          customizationId: options.customizationId,
          name: customizationName.trim(),
          cityUid: selectedCityId,
          data: {
            LstItem_Options: lstItemOptions
          },
          listItem: selectedDishCodes,
          sort: 1000,
          isUpdateSameCustomization: false,
          existingCustomization: existingCustomization || undefined
        })
        toast.success('Đã cập nhật customization thành công!')
      } else {
        await createCustomizationMutation.mutateAsync({
          name: customizationName.trim(),
          cityUid: selectedCityId,
          data: {
            LstItem_Options: lstItemOptions
          },
          listItem: selectedDishCodes,
          sort: 1000,
          isUpdateSameCustomization: false
        })
        toast.success('Đã tạo customization thành công!')
      }

      options.onSuccess?.()
      navigate({ to: '/menu/customization/customization-in-city' })
    } catch {
      const errorMessage = options.isEdit
        ? 'Lỗi khi cập nhật customization. Vui lòng thử lại.'
        : 'Lỗi khi tạo customization. Vui lòng thử lại.'
      toast.error(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  return {
    // State
    customizationName,
    selectedCityId,
    isSubmitting,
    existingCustomization,
    customizationId: options.customizationId,

    // Actions
    setCustomizationName,
    setSelectedCityId,
    setExistingCustomization,
    handleBack,
    handleSave,

    // Computed
    isFormValid: customizationName.trim() && selectedCityId
  }
}
