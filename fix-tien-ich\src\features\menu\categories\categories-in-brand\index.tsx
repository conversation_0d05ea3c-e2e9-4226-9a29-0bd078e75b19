import { useState } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { toast } from 'sonner'

import { ItemType } from '@/lib/item-types-api'

import { useCreateItemCategory, useDeleteItemType, useItemTypesData, useUpdateItemTypeStatus } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ConfirmModal, SkeletonTable } from '@/components/pos'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { categoryColumns, CategoryDataTable, ActionBar } from './components'

function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  return 'Đã xảy ra lỗi không xác định'
}

export default function CategoriesInBrandPage() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<ItemType | null>(null)

  const {
    data: categories,
    isLoading,
    error
  } = useItemTypesData({
    search: searchTerm || undefined,
    enabled: true
  })

  const createCategoryMutation = useCreateItemCategory()
  const updateStatusMutation = useUpdateItemTypeStatus()
  const deleteCategoryMutation = useDeleteItemType()

  const handleCreateCategory = () => {
    navigate({ to: '/menu/category/detail' })
  }

  const handleToggleStatus = async (category: ItemType) => {
    try {
      const updatedCategory = {
        ...category,
        active: category.active === 1 ? 0 : 1
      }

      await updateStatusMutation.mutateAsync(updatedCategory)
      const statusText = updatedCategory.active === 1 ? 'kích hoạt' : 'vô hiệu hóa'
      toast.success(`${statusText} nhóm "${category.item_type_name}" thành công!`)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleEditCategory = (category: ItemType) => {
    navigate({ to: '/menu/category/detail/$id', params: { id: category.id } })
  }

  const handleRowClick = (category: ItemType) => {
    navigate({ to: '/menu/category/detail/$id', params: { id: category.id } })
  }

  const handleDeleteCategory = (category: ItemType) => {
    setSelectedCategory(category)
    setConfirmModalOpen(true)
  }

  const handleConfirmDelete = async () => {
    if (!selectedCategory) return

    try {
      await deleteCategoryMutation.mutateAsync(selectedCategory.id)
      toast.success(`Xóa nhóm "${selectedCategory.item_type_name}" thành công!`)
      setConfirmModalOpen(false)
      setSelectedCategory(null)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <ActionBar
            searchQuery={searchQuery}
            onSearchQueryChange={setSearchQuery}
            onSearchSubmit={() => setSearchTerm(searchQuery)}
            onCreateCategory={handleCreateCategory}
            isCreating={createCategoryMutation.isPending}
          />

          {error && (
            <div className='py-8 text-center'>
              <p className='text-red-600'>{getErrorMessage(error)}</p>
            </div>
          )}

          {isLoading && <SkeletonTable rows={8} columns={5} />}

          {!error && !isLoading && (
            <CategoryDataTable
              columns={categoryColumns}
              data={categories || []}
              onEditCategory={handleEditCategory}
              onDeleteCategory={handleDeleteCategory}
              onToggleStatus={handleToggleStatus}
              onRowClick={handleRowClick}
            />
          )}

          <ConfirmModal
            open={confirmModalOpen}
            onOpenChange={setConfirmModalOpen}
            content={
              selectedCategory
                ? `Bạn có muốn xóa nhóm "${selectedCategory.item_type_name}"?`
                : 'Bạn có muốn xóa nhóm này?'
            }
            confirmText='Xác nhận'
            onConfirm={handleConfirmDelete}
            isLoading={deleteCategoryMutation.isPending}
          />
        </div>
      </Main>
    </>
  )
}
