import { IconDownload, IconUpload } from '@tabler/icons-react'

import { PosModal } from '@/components/pos'
import { Button } from '@/components/ui'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ParsedCustomizationData } from '@/types/customizations'

interface City {
  id: string
  city_name: string
}

interface ExportCustomizationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  showParsedData: boolean
  exportCityId: string
  onExportCityIdChange: (cityId: string) => void
  cities: City[]
  selectedFile: File | null
  parsedData: ParsedCustomizationData[]
  isExporting: boolean
  isSaving: boolean
  onCancel: () => void
  onConfirm: () => void
  onDownloadExportFile: () => void
  onUploadFile: () => void
}

export function ExportCustomizationModal({
  open,
  onOpenChange,
  showParsedData,
  exportCityId,
  onExportCityIdChange,
  cities,
  selectedFile,
  parsedData,
  isExporting,
  isSaving,
  onCancel,
  onConfirm,
  onDownloadExportFile,
  onUploadFile,
}: ExportCustomizationModalProps) {
  return (
    <PosModal
      title='Xuất, sửa customization'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      confirmText={showParsedData ? 'Lưu' : 'Tiếp tục'}
      cancelText='Hủy'
      centerTitle={true}
      maxWidth={showParsedData ? 'sm:max-w-6xl' : 'sm:max-w-[400px]'}
      isLoading={isSaving}
    >
      <div className='space-y-4'>
        {!showParsedData && (
          <>
            <div className='rounded-md border p-3'>
              <div className='mb-3 font-medium'>Bước 1. Chỉnh bộ lọc để xuất file</div>
              <Select value={exportCityId} onValueChange={onExportCityIdChange}>
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Chọn thành phố' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>Tất cả thành phố</SelectItem>
                  {cities.map(city => (
                    <SelectItem key={city.id} value={city.id}>
                      {city.city_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className='rounded-md border p-3'>
              <div className='mb-3 font-medium'>Bước 2. Tải file dữ liệu</div>
              <div className='flex items-center justify-between'>
                <div className='flex flex-col'>
                  <span className='text-sm text-gray-600'>Tải xuống</span>
                </div>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={onDownloadExportFile}
                  disabled={isExporting}
                  className='h-8 w-8 p-0'
                >
                  <IconDownload className='h-4 w-4' />
                </Button>
              </div>
            </div>

            <div className='rounded-md border p-3'>
              <div className='mb-3 font-medium'>Bước 3. Thêm cấu hình vào file</div>
              <div className='rounded-md bg-yellow-50 p-3 text-sm text-yellow-800'>
                <strong>Không sửa các cột:</strong> ID, Thành phố.
              </div>
            </div>

            <div className='rounded-md border p-3'>
              <div className='mb-3 font-medium'>Bước 4. Tải file lên</div>
              <div className='flex items-center justify-between'>
                <div className='flex flex-col'>
                  <span className='text-xs text-gray-600'>
                    Sau khi đã điền đầy đủ bạn có thể tải file lên
                  </span>
                  {selectedFile && (
                    <span className='mt-1 text-xs text-gray-500'>
                      File đã chọn: {selectedFile.name}
                    </span>
                  )}
                </div>
                <Button
                  size='sm'
                  variant='default'
                  onClick={onUploadFile}
                  className='h-8 w-8 p-0'
                >
                  <IconUpload className='h-4 w-4' />
                </Button>
              </div>
            </div>
          </>
        )}
        {showParsedData && (
          <div className='max-h-60 overflow-y-auto rounded-md border'>
            <table className='w-full text-sm'>
              <thead className='bg-gray-50'>
                <tr>
                  <th className='border-b px-3 py-2 text-left'>Tên</th>
                  <th className='border-b px-3 py-2 text-left'>Thành phố</th>
                  <th className='border-b px-3 py-2 text-left'>Mã món áp dụng</th>
                  <th className='border-b px-3 py-2 text-left'>Tên nhóm</th>
                  <th className='border-b px-3 py-2 text-left'>Yêu cầu chọn</th>
                  <th className='border-b px-3 py-2 text-left'>Giới hạn chọn</th>
                  <th className='border-b px-3 py-2 text-left'>Mã món theo nhóm</th>
                </tr>
              </thead>
              <tbody>
                {parsedData.map((item, index) => (
                  <tr key={index} className='border-b'>
                    <td className='px-3 py-2'>{item.name}</td>
                    <td className='px-3 py-2'>{item.cityName}</td>
                    <td className='px-3 py-2'>{item.appliedItemCodes}</td>
                    <td className='px-3 py-2'>{item.groupName}</td>
                    <td className='px-3 py-2'>{item.minRequired}</td>
                    <td className='px-3 py-2'>{item.maxAllowed}</td>
                    <td className='px-3 py-2'>{item.groupItemCodes}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </PosModal>
  )
}
