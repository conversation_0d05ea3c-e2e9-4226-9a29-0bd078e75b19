import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { ComboSpecialActionBar } from './action-bar'

import type { ItemsFilters } from '../types'

interface DanhSachComboTuyChinhFiltersProps {
  filters: ItemsFilters
  onFiltersChange: (filters: Partial<ItemsFilters>) => void
}

export function DanhSachComboTuyChinhFilters({ filters, onFiltersChange }: DanhSachComboTuyChinhFiltersProps) {
  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value })
  }

  const handleStatusChange = (value: string) => {
    onFiltersChange({ status: value })
  }

  const handleCanTakeawayChange = (value: string) => {
    onFiltersChange({ canTakeaway: value })
  }

  const handleCanDeliveryChange = (value: string) => {
    onFiltersChange({ canDelivery: value })
  }

  const handleFilter = () => {
    console.log('Applying filters:', filters)
  }

  const handleViewPreview = () => {
    console.log('Xem trước')
  }



  const handleSyncItems = () => {
    console.log('Đồng bộ combo tùy chỉnh')
  }

  const getItemCount = () => {
    // Mock count for custom combos
    return 0
  }

  return (
    <div className='space-y-4 mb-6'>
      {/* Filters */}
      <div className='flex items-center gap-2'>
        <div className='w-[220px]'>
          <Input
            placeholder='TÊN/MÃ COMBO'
            value={filters.search}
            onChange={(e) => handleSearchChange(e.target.value)}
            className='h-10'
          />
        </div>
        <div className='w-[160px]'>
          <Select value={filters.status} onValueChange={handleStatusChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='TRẠNG THÁI' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='available'>Có bán</SelectItem>
              <SelectItem value='unavailable'>Hết hàng</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='w-[160px]'>
          <Select value={filters.canTakeaway} onValueChange={handleCanTakeawayChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='BÁN TẠI CHỖ' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='true'>Có</SelectItem>
              <SelectItem value='false'>Không</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='w-[160px]'>
          <Select value={filters.canDelivery} onValueChange={handleCanDeliveryChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='BÁN MANG ĐI' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='true'>Có</SelectItem>
              <SelectItem value='false'>Không</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='ml-2'>
          <Button onClick={handleFilter} className='h-10 px-6 bg-blue-600 text-white hover:bg-blue-700'>
            Lọc
          </Button>
        </div>
      </div>

      {/* Actions */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div className='flex items-center gap-4'>
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium'>SỐ LƯỢNG: {getItemCount()}</span>
            <button className='text-blue-600 text-sm hover:underline'>
              Sắp xếp thứ tự hiển thị
            </button>
          </div>
        </div>

        <ComboSpecialActionBar
          onSyncItems={handleSyncItems}
          onViewPreview={handleViewPreview}
        />
      </div>
    </div>
  )
}
