# Husky Pre-commit ESLint Setup

## Overview
This document describes the <PERSON>sky pre-commit hook setup with ESLint configuration that enforces a maximum of 350 lines per file.

## Components Installed

### 1. <PERSON>sky
- **Package**: `husky@9.1.7`
- **Purpose**: Git hooks management
- **Configuration**: `.husky/pre-commit` hook

### 2. Lint-staged
- **Package**: `lint-staged@16.1.2`
- **Purpose**: Run linters only on staged files for better performance
- **Configuration**: `lint-staged` section in `package.json`

### 3. ESLint Configuration
- **File**: `eslint.config.js`
- **New Rule**: `max-lines` with 350 line limit
- **Configuration**:
  ```javascript
  'max-lines': [
    'error',
    {
      max: 350,
      skipBlankLines: true,
      skipComments: true,
    },
  ]
  ```

## How It Works

### Pre-commit Hook
When you commit code, the pre-commit hook automatically:
1. Runs ESLint on staged TypeScript/JavaScript files
2. Runs Prettier on staged files
3. Fixes auto-fixable issues
4. Prevents commit if there are unfixable errors

### File Structure
```
.husky/
├── _/                    # Husky internal files
└── pre-commit           # Pre-commit hook script

package.json             # Contains lint-staged configuration
eslint.config.js         # ESLint configuration with max-lines rule
```

### Package.json Scripts
- `lint`: Run ESLint on all files
- `lint:fix`: Run ESLint with auto-fix on all files
- `prepare`: Initialize Husky (runs on npm/pnpm install)

### Lint-staged Configuration
```json
{
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{js,jsx,json,css,md}": [
      "prettier --write"
    ]
  }
}
```

## Usage

### Automatic Usage
The pre-commit hook runs automatically when you commit:
```bash
git add .
git commit -m "Your commit message"
# Hook runs automatically and checks staged files
```

### Manual Usage
You can also run the checks manually:
```bash
# Run ESLint on all files
pnpm lint

# Run ESLint with auto-fix
pnpm lint:fix

# Run lint-staged manually
pnpm exec lint-staged
```

## Max Lines Rule
The ESLint configuration enforces a maximum of 350 lines per file:
- **Blank lines**: Excluded from count
- **Comment lines**: Excluded from count
- **Error level**: Will prevent commits if exceeded

### Example Error
```
/path/to/file.ts
  351:1  error  File has too many lines (782). Maximum allowed is 350  max-lines
```

## Benefits
1. **Code Quality**: Enforces consistent code organization
2. **Performance**: Only checks staged files, not entire codebase
3. **Automatic Formatting**: Prettier runs automatically
4. **Prevention**: Stops problematic code from being committed
5. **Maintainability**: Encourages breaking down large files

## Troubleshooting

### Skip Hooks (Emergency)
If you need to bypass the hooks in an emergency:
```bash
git commit --no-verify -m "Emergency commit"
```

### Hook Not Running
If the hook isn't running, ensure it's executable:
```bash
chmod +x .husky/pre-commit
```

### Reinstall Hooks
If hooks are missing after cloning:
```bash
pnpm install  # This runs the prepare script
```

## File Size Guidelines
When you encounter the max-lines error:
1. **Refactor**: Break large files into smaller, focused modules
2. **Extract**: Move reusable code to utility files
3. **Separate**: Split different concerns into different files
4. **Organize**: Use proper folder structure for related files

This setup ensures code quality and maintainability by preventing overly large files from being committed to the repository.
