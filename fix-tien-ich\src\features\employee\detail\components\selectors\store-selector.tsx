import { IconChevronDown, IconChevronUp } from '@tabler/icons-react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  Label,
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui'
import { useStoreSelector } from '../../hooks'

interface StoreSelectorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedStore: string
  onSave: (selectedStore: string) => void
}

export function StoreSelector({
  open,
  onOpenChange,
  selectedStore,
  onSave,
}: StoreSelectorProps) {
  const {
    // Search
    searchTerm,
    setSearchTerm,

    // Data
    filteredData,

    // Expansion state
    expandedBrands,
    toggleBrandExpansion,

    // Selection state
    localSelectedStore,
    setLocalSelectedStore,
    resetSelection,
  } = useStoreSelector({ selectedStore })

  const handleSave = () => {
    onSave(localSelectedStore)
    onOpenChange(false)
  }

  const handleCancel = () => {
    resetSelection()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle>Chọn cửa hàng</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          <div>
            <Input
              placeholder='Tìm kiếm cửa hàng...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className='max-h-96 overflow-y-auto'>
            <RadioGroup
              value={localSelectedStore}
              onValueChange={setLocalSelectedStore}
            >
              {filteredData.map((brand) => (
                <div key={brand.id} className='space-y-2'>
                  <div
                    className='flex cursor-pointer items-center gap-2 rounded-md bg-gray-100 p-2 hover:bg-gray-200'
                    onClick={() => toggleBrandExpansion(brand.id)}
                  >
                    {expandedBrands.has(brand.id) ? (
                      <IconChevronDown className='h-4 w-4' />
                    ) : (
                      <IconChevronUp className='h-4 w-4' />
                    )}
                    <span className='font-medium'>{brand.brand_name}</span>
                  </div>

                  {expandedBrands.has(brand.id) && (
                    <div className='ml-6 space-y-2'>
                      {brand.cities.map((city) => (
                        <div key={city.id} className='space-y-1'>
                          <div className='ml-4 space-y-1'>
                            {city.stores.map((store) => (
                              <div
                                key={store.id}
                                className='flex items-center space-x-2'
                              >
                                <RadioGroupItem
                                  value={`store:${store.id}`}
                                  id={`store-${store.id}`}
                                />
                                <Label
                                  htmlFor={`store-${store.id}`}
                                  className='text-sm'
                                >
                                  {store.store_name}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </RadioGroup>
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel}>
            Hủy
          </Button>
          <Button onClick={handleSave} disabled={!localSelectedStore}>
            Lưu
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
