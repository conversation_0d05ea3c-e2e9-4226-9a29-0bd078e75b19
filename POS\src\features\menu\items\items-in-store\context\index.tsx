import React, { useState } from 'react'

import useDialogState from '@/hooks/ui/use-dialog-state'

import { ItemsInStore } from '../data'

export type ItemsInStoreDialogType =
  | 'delete'
  | 'export'
  | 'import'
  | 'buffet-config'
  | 'config-price-by-source'
  | 'config-time-frame'
  | 'sort-menu'
  | 'copy-menu'

interface ItemsInStoreContextType {
  open: ItemsInStoreDialogType | null
  setOpen: (str: ItemsInStoreDialogType | null) => void
  currentRow: ItemsInStore | null
  setCurrentRow: React.Dispatch<React.SetStateAction<ItemsInStore | null>>
  selectedStoreUid?: string
  setSelectedStoreUid?: (storeUid: string) => void
}

const ItemsInStoreContext = React.createContext<ItemsInStoreContextType | null>(null)

interface Props {
  children: React.ReactNode
  selectedStoreUid?: string
  setSelectedStoreUid?: (storeUid: string) => void
}

export default function ItemsInStoreProvider({ children, selectedStoreUid, setSelectedStoreUid }: Props) {
  const [open, setOpen] = useDialogState<ItemsInStoreDialogType>(null)
  const [currentRow, setCurrentRow] = useState<ItemsInStore | null>(null)

  return (
    <ItemsInStoreContext
      value={{
        open,
        setOpen,
        currentRow,
        setCurrentRow,
        selectedStoreUid,
        setSelectedStoreUid
      }}
    >
      {children}
    </ItemsInStoreContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useItemsInStore = () => {
  const itemsInStoreContext = React.useContext(ItemsInStoreContext)

  if (!itemsInStoreContext) {
    throw new Error('useItemsInStore has to be used within <ItemsInStoreContext>')
  }

  return itemsInStoreContext
}
