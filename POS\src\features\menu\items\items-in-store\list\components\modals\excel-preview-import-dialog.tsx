import { useState, useEffect } from 'react'

import { useC<PERSON>rent<PERSON>rand } from '@/stores'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { useItemTypesData, useItemClassesData, useUnitsData, useStoreData } from '@/hooks/api'

import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { useBulkCreateItemsInStore, type BulkCreateItemInStoreRequest } from '../../../hooks'

export interface ImportItem {
  id: string
  item_id: string
  city_name: string
  store_name: string
  item_name: string
  ots_price: number
  active: number
  item_id_barcode: string
  is_eat_with: number
  no_update_quantity_toping: number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

interface ImportPreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: ImportItem[]
  storeUid?: string
}

export function ImportPreviewDialog({ open, onOpenChange, data, storeUid }: ImportPreviewDialogProps) {
  const [importData, setImportData] = useState<ImportItem[]>(data)
  const [isProcessing, setIsProcessing] = useState(false)

  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  // Debug logging
  useEffect(() => {
    console.log('ImportPreviewDialog - storeUid:', storeUid)
    console.log('ImportPreviewDialog - company:', company?.id)
    console.log('ImportPreviewDialog - selectedBrand:', selectedBrand?.id)
  }, [storeUid, company?.id, selectedBrand?.id])

  const { bulkCreateItemsInStore, isBulkCreating } = useBulkCreateItemsInStore()

  // Get store data to extract city_uid
  const { data: storeData } = useStoreData(storeUid || '', !!storeUid)

  // Get reference data for mapping
  const { data: itemTypes = [] } = useItemTypesData({
    skip_limit: true,
    ...(storeUid && storeUid !== 'all' ? { store_uid: storeUid } : {})
  })
  const { data: itemClasses = [] } = useItemClassesData({
    skip_limit: true
  })
  const { data: units = [] } = useUnitsData()

  useEffect(() => {
    setImportData(data)
  }, [data])

  const handleConfirm = async () => {
    if (!storeUid || !company?.id || !selectedBrand?.id) {
      toast.error('Thiếu thông tin cửa hàng hoặc thương hiệu')
      return
    }

    if (importData.length === 0) {
      toast.error('Không có dữ liệu để import')
      return
    }

    setIsProcessing(true)

    try {
      const transformedData: BulkCreateItemInStoreRequest[] = importData.map(item => {
        const unit = units.find(u => u.unit_id === item.unit_id)

        const itemType = itemTypes.find(
          it => it.item_type_id === item.item_type_id || it.item_type_name === item.item_type_name
        )

        const itemClass = itemClasses.find(
          ic => ic.item_class_id === item.item_class_id || ic.item_class_name === item.item_class_name
        )

        const defaultUnit = units.find(u => u.unit_id === 'MON')
        const defaultItemType = itemTypes.find(it => it.item_type_name === 'LOẠI KHÁC')
        return {
          store_uid: storeUid,
          apply_with_store: 2,
          company_uid: company.id,
          brand_uid: selectedBrand.id,
          city_uid: storeData?.cityId || '',
          item_id: item.item_id,
          unit_uid: unit?.id || defaultUnit?.id || '', // Default: MON
          ots_price: item.ots_price || 0,
          ta_price: item.ots_price || 0,
          ots_tax: (item.ots_tax || 0) / 100,
          ta_tax: (item.ots_tax || 0) / 100,
          item_name: item.item_name,
          item_id_barcode: item.item_id_barcode || '',
          is_eat_with: item.is_eat_with || 0,
          item_type_uid: itemType?.id || defaultItemType?.id || '', // Default: ITEM_TYPE_OTHER
          item_class_uid: itemClass?.id || null,
          description: item.description || '',
          item_id_mapping: String(item.sku || ''),
          time_cooking: (item.time_cooking || 0) * 60000,
          time_sale_date_week: item.time_sale_date_week || 0,
          time_sale_hour_day: item.time_sale_hour_day || 0,
          sort: item.list_order || 1,
          image_path_thumb: '',
          image_path: item.image_path || '',
          extra_data: {
            no_update_quantity_toping: item.no_update_quantity_toping || 0,
            enable_edit_price: item.price_change || 0,
            is_virtual_item: item.is_virtual_item || 0,
            is_item_service: item.is_item_service || 0,
            is_buffet_item: item.is_buffet_item || 0
          }
        }
      })

      await bulkCreateItemsInStore(transformedData)
      onOpenChange(false)
    } catch (error) {
      console.error('Error creating items:', error)
      toast.error('Có lỗi xảy ra khi tạo món ăn')
    } finally {
      setIsProcessing(false)
    }
  }

  const columns = [
    { key: 'item_name', label: 'Tên', width: '200px' },
    { key: 'ots_price', label: 'Giá', width: '100px' },
    { key: 'item_id', label: 'Mã món', width: '120px' },
    { key: 'item_id_barcode', label: 'Mã barcode', width: '120px' },
    { key: 'is_eat_with', label: 'Món ăn kèm', width: '120px' },
    { key: 'no_update_quantity_toping', label: 'Không cập nhật số lượng món ăn kèm', width: '220px' },
    { key: 'item_type_id', label: 'Nhóm', width: '120px' },
    { key: 'item_class_id', label: 'Loại món', width: '120px' },
    { key: 'description', label: 'Mô tả', width: '200px' },
    { key: 'sku', label: 'SKU', width: '100px' },
    { key: 'unit_id', label: 'Đơn vị', width: '100px' },
    { key: 'ots_tax', label: 'VAT (%)', width: '80px' },
    { key: 'time_cooking', label: 'Thời gian chế biến (phút)', width: '180px' },
    { key: 'price_change', label: 'Cho phép sửa giá khi bán', width: '180px' },
    { key: 'is_virtual_item', label: 'Cấu hình món ảo', width: '150px' },
    { key: 'is_item_service', label: 'Cấu hình món dịch vụ', width: '180px' },
    { key: 'is_buffet_item', label: 'Cấu hình món ăn là vé buffet', width: '200px' },
    { key: 'time_sale_date_week', label: 'Ngày', width: '80px' },
    { key: 'time_sale_hour_day', label: 'Giờ', width: '80px' },
    { key: 'image_path', label: 'Hình ảnh', width: '120px' },
    { key: 'inqr_formula', label: 'Công thức inQR cho máy pha trà', width: '220px' }
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] max-w-7xl sm:max-w-4xl'>
        <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <DialogTitle className='text-xl font-semibold'>Thêm mới</DialogTitle>
        </DialogHeader>

        <div className='space-y-4 overflow-hidden'>
          <ScrollArea className='h-[60vh] w-full rounded-md border'>
            <Table>
              <TableHeader className='sticky top-0 z-10 bg-white'>
                <TableRow>
                  <TableHead className='w-16 text-center'>Thứ tự</TableHead>
                  {columns.map(column => (
                    <TableHead key={column.key} style={{ width: column.width }}>
                      {column.label}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {importData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className='text-center font-medium'>{index + 1}</TableCell>
                    {columns.map(column => (
                      <TableCell key={column.key} style={{ width: column.width }}>
                        {column.key === 'ots_price' ? (
                          <span className='text-right'>{item[column.key]?.toLocaleString('vi-VN')} ₫</span>
                        ) : column.key === 'active' ? (
                          <span>{item[column.key]}</span>
                        ) : column.key === 'item_id' || column.key === 'item_id_barcode' ? (
                          <span className='font-mono text-sm'>{item[column.key]}</span>
                        ) : column.key === 'item_name' ? (
                          <span className='font-medium'>{item[column.key]}</span>
                        ) : column.key === 'is_eat_with' ||
                          column.key === 'no_update_quantity_toping' ||
                          column.key === 'price_change' ||
                          column.key === 'is_virtual_item' ||
                          column.key === 'is_item_service' ||
                          column.key === 'is_buffet_item' ? (
                          <span className='text-center'>{item[column.key]}</span>
                        ) : (
                          <span>{item[column.key] || ''}</span>
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <ScrollBar orientation='horizontal' />
            <ScrollBar orientation='vertical' />
          </ScrollArea>

          <div className='flex items-center justify-between border-t pt-4'>
            <Button variant='outline' onClick={() => onOpenChange(false)}>
              Đóng
            </Button>
            <Button onClick={handleConfirm} disabled={isProcessing || isBulkCreating || !storeUid}>
              {isProcessing || isBulkCreating ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
