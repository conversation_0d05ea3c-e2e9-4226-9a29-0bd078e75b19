import { useEffect, useRef } from 'react'

import { MissingIdError, LoadingError, LoadingSpinner } from '@/components'
import { ExistingCustomization } from '@/types'

import { useItemsData, useCustomizationById } from '@/hooks/api'

import { CreateGroupModal, DishSelectionModal, CustomizationHeader, BasicForm } from './components'
import { FormProvider, useFormContext } from './form-context'

function DetailPageContent() {
  const { customizationForm, groupManagement, dishSelection, handlers } = useFormContext()
  const hasInitializedDishes = useRef(false)

  const customizationId = customizationForm.customizationId
  const {
    data: existingCustomization,
    isLoading: isLoadingCustomization,
    error: customizationError
  } = useCustomizationById(customizationId || '', !!customizationId)

  const {
    data: items = [],
    isLoading: isLoadingItems,
    error: itemsError
  } = useItemsData({
    params: {
      city_uid: customizationForm.selectedCityId,
      skip_limit: true
    },
    enabled: !!customizationForm.selectedCityId
  })

  useEffect(() => {
    if (existingCustomization && !isLoadingCustomization) {
      customizationForm.setExistingCustomization(existingCustomization as unknown as ExistingCustomization)
      customizationForm.setCustomizationName(existingCustomization.name)
      customizationForm.setSelectedCityId(existingCustomization.cityUid || '')

      if (existingCustomization.data?.LstItem_Options && items.length > 0) {
        const groups = existingCustomization.data.LstItem_Options.map(option => ({
          id: option.id,
          name: option.Name,
          minRequired: option.Min_Permitted,
          maxAllowed: option.Max_Permitted,
          items: option.LstItem_Id.map(itemId => {
            const foundItem = items.find((apiItem: any) => apiItem.item_id === itemId)
            return {
              id: foundItem?.id || itemId,
              name: foundItem?.item_name || itemId,
              price: foundItem?.ots_price || 0,
              code: itemId,
              active: foundItem?.active ?? 1
            }
          })
        }))
        groupManagement.setCustomizationGroups(groups)
      }
    }
  }, [existingCustomization, isLoadingCustomization, items])

  useEffect(() => {
    if (items.length > 0) {
      if (groupManagement.customizationGroups.length > 0) {
        const updatedGroups = groupManagement.customizationGroups.map(group => ({
          ...group,
          items: group.items.map(item => {
            const foundItem = items.find((apiItem: any) => apiItem.item_id === item.code)
            return foundItem
              ? {
                  id: foundItem.id,
                  name: foundItem.item_name,
                  price: foundItem.ots_price,
                  code: foundItem.item_id,
                  active: foundItem.active
                }
              : item
          })
        }))
        groupManagement.setCustomizationGroups(updatedGroups)
      }

      if (
        existingCustomization?.listItem &&
        existingCustomization.listItem.length > 0 &&
        !hasInitializedDishes.current
      ) {
        const dishIds = new Set<string>()
        existingCustomization.listItem.forEach(itemCode => {
          const foundItem = items.find((apiItem: any) => apiItem.item_id === itemCode)
          if (foundItem) {
            dishIds.add(foundItem.id)
          }
        })
        dishSelection.setSelectedDishes(dishIds)
        hasInitializedDishes.current = true
      }
    }
  }, [items, existingCustomization])

  const isError = customizationError || (itemsError && customizationForm.selectedCityId)

  if (customizationId && !existingCustomization && !isLoadingCustomization && !customizationError) {
    return <MissingIdError />
  }

  if (isLoadingCustomization) {
    return <LoadingSpinner />
  }

  if (isError) {
    return <LoadingError description='Không thể tải dữ liệu' />
  }

  const handleSave = async () => {
    await customizationForm.handleSave(groupManagement.customizationGroups, dishSelection.selectedDishes, items)
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <CustomizationHeader
        onBack={customizationForm.handleBack}
        onSave={handleSave}
        isSubmitting={customizationForm.isSubmitting}
        isFormValid={!!customizationForm.isFormValid}
        title={customizationId ? 'Sửa customization' : 'Tạo customization'}
        saveButtonText={customizationId ? 'Cập nhật' : 'Lưu'}
        submittingText={customizationId ? 'Đang cập nhật...' : 'Đang tạo...'}
      />

      <BasicForm
        customizationId={customizationId}
        isLoadingItems={isLoadingItems}
        handleCreateGroup={handlers.handleCreateGroup}
        handleEditGroup={handlers.handleEditGroup}
      />

      <CreateGroupModal items={items} />

      <DishSelectionModal items={items} />
    </div>
  )
}

interface CustomizationDetailPageProps {
  customizationId?: string
}

export default function CustomizationDetailPage({ customizationId }: CustomizationDetailPageProps) {
  return (
    <FormProvider customizationId={customizationId}>
      <DetailPageContent />
    </FormProvider>
  )
}
