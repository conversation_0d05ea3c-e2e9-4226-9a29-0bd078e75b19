import { useMemo, useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { useCustomizationsData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import MenuItemsProvider, { useItemsInStore } from '../context'
import { ItemsInStore } from '../data'
import {
  useItemsInStoreForTable,
  useUpdateItemInStoreStatus,
  useItemsInStoreListState,
  useUpdateItemInStore,
  UpdateItemInStoreRequest
} from '../hooks'
import {
  createColumns,
  ItemsInStoreButtons,
  ItemsInStoreTableSkeleton,
  ItemsInStoreDialogs,
  ItemsInStoreDataTable,
  CustomizationDialog,
  BuffetConfigModal
} from './components'

interface ItemsInCityContentProps {
  selectedStoreUid: string
  setSelectedStoreUid: (storeUid: string) => void
}

function ItemsInCityContent({ selectedStoreUid, setSelectedStoreUid }: ItemsInCityContentProps) {
  const [currentPage, setCurrentPage] = useState<number>(1)
  const navigate = useNavigate()
  const { setOpen, setCurrentRow } = useItemsInStore()
  const { updateStatusAsync } = useUpdateItemInStoreStatus()
  const { updateItemAsync } = useUpdateItemInStore()
  const {
    isCustomizationDialogOpen,
    isBuffetItem,
    isBuffetConfigModalOpen,
    setIsCustomizationDialogOpen,
    setIsBuffetItem,
    selectedMenuItem,
    setSelectedMenuItem,
    setIsBuffetConfigModalOpen,
    selectedBuffetMenuItem,
    setSelectedBuffetMenuItem,
    selectedItemTypeUid,
    setSelectedItemTypeUid,
    selectedDaysOfWeek,
    setSelectedDaysOfWeek,
    selectedStatus,
    setSelectedStatus,
    selectedApplyWithStore,
    setSelectedApplyWithStore
  } = useItemsInStoreListState()

  const filterParams = useMemo(
    () => ({
      ...(selectedItemTypeUid !== 'all' && { item_type_uid: selectedItemTypeUid }),
      ...(selectedStoreUid !== 'all' && { store_uid: selectedStoreUid }),
      ...(selectedDaysOfWeek.length > 0 && { time_sale_date_week: selectedDaysOfWeek.join(',') }),
      ...(selectedStatus !== 'all' && { active: parseInt(selectedStatus, 10) }),
      ...(selectedApplyWithStore === '-1' && { apply_with_store: parseInt(selectedApplyWithStore, 10) }),
      page: currentPage
    }),
    [selectedItemTypeUid, selectedStoreUid, selectedDaysOfWeek, selectedStatus, selectedApplyWithStore, currentPage]
  )
  const {
    data: items = [],
    isLoading: itemsLoading,
    error: itemsError,
    hasNextPage
  } = useItemsInStoreForTable({
    params: filterParams
  })

  const { data: customizations = [] } = useCustomizationsData({ skip_limit: true, store_uid: selectedStoreUid })
  useEffect(() => {
    setCurrentPage(1)
  }, [selectedItemTypeUid, selectedStoreUid, selectedDaysOfWeek, selectedStatus, selectedApplyWithStore])

  const handleCustomizationClick = (item: ItemsInStore) => {
    setSelectedMenuItem(item)
    setIsCustomizationDialogOpen(true)
  }

  const handleBuffetConfigClick = (item: ItemsInStore) => {
    setSelectedMenuItem(item)
    setSelectedBuffetMenuItem(item?.extra_data?.exclude_items_buffet || [])
    setIsBuffetItem(item?.extra_data?.is_buffet_item === 1)
    setIsBuffetConfigModalOpen(true)
  }

  const handleCopyClick = (item: ItemsInStore) => {
    navigate({ to: '/menu/items/items-in-store/detail', search: { id: item.id || '' } })
  }

  const handleDeleteClick = (item: ItemsInStore) => {
    setCurrentRow(item)
    setOpen('delete')
  }

  const handleRowClick = (item: ItemsInStore) => {
    navigate({ to: '/menu/items/items-in-store/detail/$id', params: { id: item.id || '' } })
  }

  const handleToggleStatus = async (item: ItemsInStore) => {
    const newStatus = item.active ? 0 : 1
    await updateStatusAsync({
      id: item.id || '',
      active: newStatus
    })
  }

  const columns = createColumns({ onBuffetConfigClick: handleBuffetConfigClick })

  if (itemsError) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu</p>
          <p className='text-muted-foreground text-xs'>
            {itemsError && `Món ăn: ${itemsError?.message || 'Lỗi không xác định'}`}
          </p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Món ăn tại cửa hàng</h2>
          </div>
          <ItemsInStoreButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          {itemsLoading && <ItemsInStoreTableSkeleton />}
          {!itemsLoading && (
            <ItemsInStoreDataTable
              columns={columns}
              data={items}
              onCustomizationClick={handleCustomizationClick}
              onCopyClick={handleCopyClick}
              onToggleStatus={handleToggleStatus}
              onRowClick={handleRowClick}
              onDeleteClick={handleDeleteClick}
              customizations={customizations}
              selectedItemTypeUid={selectedItemTypeUid}
              onItemTypeChange={setSelectedItemTypeUid}
              selectedStoreUid={selectedStoreUid}
              onStoreChange={setSelectedStoreUid}
              selectedDaysOfWeek={selectedDaysOfWeek}
              onDaysOfWeekChange={setSelectedDaysOfWeek}
              selectedStatus={selectedStatus}
              onStatusChange={setSelectedStatus}
              selectedApplyWithStore={selectedApplyWithStore}
              onApplyWithStoreChange={setSelectedApplyWithStore}
              hasNextPageOverride={hasNextPage}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
            />
          )}
        </div>
      </Main>

      <ItemsInStoreDialogs />

      {isCustomizationDialogOpen && selectedMenuItem && (
        <CustomizationDialog
          open={isCustomizationDialogOpen}
          onOpenChange={setIsCustomizationDialogOpen}
          item={selectedMenuItem}
          customizations={customizations}
        />
      )}

      {isBuffetConfigModalOpen && selectedBuffetMenuItem && (
        <BuffetConfigModal
          itemsBuffet={selectedBuffetMenuItem}
          open={isBuffetConfigModalOpen}
          onOpenChange={setIsBuffetConfigModalOpen}
          onItemsChange={async (itemsBuffetIds: string[]) => {
            console.log(isBuffetItem)
            await updateItemAsync({
              ...selectedMenuItem,
              extra_data: {
                is_buffet_item: isBuffetItem ? 1 : 0,
                exclude_items_buffet: itemsBuffetIds
              }
            } as unknown as UpdateItemInStoreRequest)
          }}
          items={items}
          hide={false}
          enable={isBuffetItem}
          onEnableChange={setIsBuffetItem}
        />
      )}
    </>
  )
}

export default function ItemsInCityPage() {
  const {
    selectedStoreUid,
    setSelectedStoreUid
  } = useItemsInStoreListState()

  return (
    <MenuItemsProvider selectedStoreUid={selectedStoreUid} setSelectedStoreUid={setSelectedStoreUid}>
      <ItemsInCityContent selectedStoreUid={selectedStoreUid} setSelectedStoreUid={setSelectedStoreUid} />
    </MenuItemsProvider>
  )
}
