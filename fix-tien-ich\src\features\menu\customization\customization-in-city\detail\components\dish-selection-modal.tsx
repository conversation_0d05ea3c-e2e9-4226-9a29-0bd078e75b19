import { ChevronDown, ChevronRight } from 'lucide-react'

import { PosModal } from '@/components/pos'
import { Checkbox, Collapsible, CollapsibleContent, CollapsibleTrigger, Input } from '@/components/ui'

import { useFormContext } from '../form-context'

interface DishSelectionModalProps {
  items: any[]
}

export function DishSelectionModal({ items }: DishSelectionModalProps) {
  const { dishSelection, modalState } = useFormContext()

  // Calculate data from context
  const selectedDishItems = dishSelection.getSelectedDishItems(items)
  const remainingDishItems = dishSelection.getRemainingDishItems(items)

  const {
    dishSearchTerm,
    setDishSearchTerm,
    selectedSectionOpen,
    setSelectedSectionOpen,
    remainingSectionOpen,
    setRemainingSectionOpen,
    selectedDishes,
    handleDishToggle
  } = dishSelection
  return (
    <PosModal
      title=''
      open={modalState.dishModalOpen}
      onOpenChange={modalState.setDishModalOpen}
      onCancel={modalState.handleCloseDishModal}
      onConfirm={() => {
        dishSelection.handleConfirmDishSelection()
        modalState.handleCloseDishModal()
      }}
      confirmText='Xác nhận'
      cancelText='Hủy'
      maxWidth='sm:max-w-2xl'
    >
      <div className='space-y-4'>
        <Input
          placeholder='Tìm kiếm'
          value={dishSearchTerm}
          onChange={e => setDishSearchTerm(e.target.value)}
          className='w-full'
        />

        <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
          <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
            <span className='font-medium'>Đã chọn ({selectedDishItems.length})</span>
            {selectedSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
          </CollapsibleTrigger>
          <CollapsibleContent className='mt-2'>
            <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
              {selectedDishItems.length === 0 && <p className='text-sm text-gray-500'>Chưa có món nào được chọn</p>}
              {selectedDishItems.length > 0 &&
                selectedDishItems
                  .sort((a: any, b: any) => (b.active || 0) - (a.active || 0))
                  .map((item: any) => {
                    const isActive = item.active === 1
                    return (
                      <label
                        key={item.id}
                        className={`flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50 ${
                          !isActive ? 'bg-gray-100 opacity-50' : ''
                        }`}
                      >
                        <Checkbox
                          checked={selectedDishes.has(item.id)}
                          onCheckedChange={() => handleDishToggle(item.id)}
                          disabled={false}
                        />
                        <div className='flex-1'>
                          <p className={`text-sm font-medium ${isActive ? '' : 'text-gray-400'}`}>{item.item_name}</p>
                          <p className={`text-xs ${isActive ? 'text-gray-500' : 'text-gray-400'}`}>
                            {item.ots_price.toLocaleString('vi-VN')} đ
                          </p>
                        </div>
                      </label>
                    )
                  })}
            </div>
          </CollapsibleContent>
        </Collapsible>

        <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
          <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
            <span className='font-medium'>Còn lại ({remainingDishItems.length})</span>
            {remainingSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
          </CollapsibleTrigger>
          <CollapsibleContent className='mt-2'>
            <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
              {remainingDishItems.length === 0 && <p className='text-sm text-gray-500'>Không có món nào</p>}
              {remainingDishItems.length > 0 &&
                remainingDishItems
                  .sort((a: any, b: any) => (b.active || 0) - (a.active || 0))
                  .map((item: any) => {
                    const isActive = item.active === 1
                    return (
                      <label
                        key={item.id}
                        className={`flex items-center space-x-3 rounded p-2 ${
                          isActive ? 'cursor-pointer hover:bg-gray-50' : 'cursor-not-allowed bg-gray-100 opacity-50'
                        }`}
                      >
                        <Checkbox
                          checked={selectedDishes.has(item.id)}
                          onCheckedChange={() => isActive && handleDishToggle(item.id)}
                          disabled={!isActive}
                        />
                        <div className='flex-1'>
                          <p className={`text-sm font-medium ${isActive ? '' : 'text-gray-400'}`}>{item.item_name}</p>
                          <p className={`text-xs ${isActive ? 'text-gray-500' : 'text-gray-400'}`}>
                            {item.ots_price.toLocaleString('vi-VN')} đ
                          </p>
                        </div>
                      </label>
                    )
                  })}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </PosModal>
  )
}
