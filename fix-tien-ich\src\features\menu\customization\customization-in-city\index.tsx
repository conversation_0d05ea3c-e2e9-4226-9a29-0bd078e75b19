import { useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { ProfileDropdown, Search, ThemeSwitch } from '@/components'
import { Customization } from '@/types'

import { useCustomizationsData } from '@/hooks/api'

import { Header, Main } from '@/components/layout'

import {
  customizationColumns,
  CustomizationDataTable,
  ActionBar,
  DeleteCustomizationModal,
  CopyCustomizationModal,
  ExportCustomizationModal,
  ImportCustomizationModal
} from './components'
import {
  useCustomizationSearch,
  useCustomizationModals,
  useCustomizationCopy,
  useCustomizationDelete,
  useCustomizationExport,
  useCustomizationImport
} from './hooks'

export default function CustomizationInCityPage() {
  const PAGE_SIZE = 20
  const [currentPage, setCurrentPage] = useState<number>(1)
  const navigate = useNavigate()

  const search = useCustomizationSearch()
  const modals = useCustomizationModals()
  const copy = useCustomizationCopy()
  const deleteHook = useCustomizationDelete()
  const exportHook = useCustomizationExport()
  const importHook = useCustomizationImport()

  const {
    data: customizations = [],
    isLoading,
    error
  } = useCustomizationsData({
    searchTerm: search.searchTerm || undefined,
    list_city_uid: search.listCityUid,
    page: currentPage,
    limit: PAGE_SIZE
  })

  const { data: nextPageData = [] } = useCustomizationsData({
    searchTerm: search.searchTerm || undefined,
    list_city_uid: search.listCityUid,
    page: currentPage + 1,
    limit: PAGE_SIZE,
    enabled: customizations.length > 0
  })

  const hasNextPage = customizations.length === 0 ? false : nextPageData.length > 0

  useEffect(() => {
    setCurrentPage(1)
  }, [search.selectedCityId, search.searchTerm])

  const handleCopyCustomization = (customization: Customization) => {
    copy.initializeCopyName(customization)
    modals.openCopyModal(customization)
  }

  const handleDeleteCustomization = (customization: Customization) => {
    modals.openDeleteModal(customization)
  }

  const handleConfirmCopyCustomization = async () => {
    if (!modals.selectedCustomization) return

    const targetCityUid = search.selectedCityId !== 'all' ? search.selectedCityId : undefined
    const success = await copy.handleCopyCustomization(modals.selectedCustomization, targetCityUid)
    if (success) modals.closeCopyModal()
  }

  const handleConfirmDeleteCustomization = async () => {
    if (!modals.selectedCustomization) return

    const success = await deleteHook.handleConfirmDelete(modals.selectedCustomization)
    if (success) modals.closeDeleteModal()
  }

  const handleCancelCopyModal = () => {
    copy.resetCopyName()
    modals.closeCopyModal()
  }

  const handleExportCustomizations = () => {
    exportHook.resetExportState()
    modals.openExportModal()
  }

  const handleCloseExportModal = () => {
    exportHook.resetExportState()
    modals.closeExportModal()
  }

  const handleSaveImportedData = async () => {
    const success = await exportHook.handleSaveImportedData()
    if (success) modals.closeExportModal()
  }

  const handleImportCustomizations = () => {
    importHook.resetImportState()
    modals.openImportModal()
  }

  const handleCloseImportModal = () => {
    importHook.resetImportState()
    modals.closeImportModal()
  }

  const handleSaveImportedCustomizations = async () => {
    const success = await importHook.handleSaveImportedCustomizations()
    if (success) modals.closeImportModal()
  }

  const handleCreateCustomization = () => {
    navigate({ to: '/menu/customization/customization-in-city/detail' })
  }

  const handleEditCustomization = (customization: Customization) => {
    navigate({
      to: '/menu/customization/customization-in-city/detail/$customizationId',
      params: { customizationId: customization.id }
    })
  }

  const columns = customizationColumns({
    onCopyCustomization: handleCopyCustomization,
    onDeleteCustomization: handleDeleteCustomization,
    currentPage,
    pageSize: PAGE_SIZE
  })

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <ActionBar
            searchQuery={search.searchQuery}
            onSearchQueryChange={search.setSearchQuery}
            onSearchKeyDown={search.handleSearchKeyDown}
            selectedCityId={search.selectedCityId}
            onCityChange={search.setSelectedCityId}
            cities={search.cities}
            onExportCustomizations={handleExportCustomizations}
            onImportCustomizations={handleImportCustomizations}
            onCreateCustomization={handleCreateCustomization}
          />

          {error && (
            <div className='mb-4 rounded-md bg-red-50 p-4 text-red-700'>
              Có lỗi xảy ra khi tải dữ liệu: {error.message}
            </div>
          )}

          <CustomizationDataTable
            columns={columns}
            data={customizations}
            isLoading={isLoading}
            onRowClick={handleEditCustomization}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
            hasNextPage={hasNextPage}
          />

          <DeleteCustomizationModal
            open={modals.confirmModalOpen}
            onOpenChange={modals.setConfirmModalOpen}
            selectedCustomization={modals.selectedCustomization}
            onCancel={modals.closeDeleteModal}
            onConfirm={handleConfirmDeleteCustomization}
            isLoading={deleteHook.isLoading}
          />

          <CopyCustomizationModal
            open={modals.copyModalOpen}
            onOpenChange={modals.setCopyModalOpen}
            selectedCustomization={modals.selectedCustomization}
            copyName={copy.copyName}
            onCopyNameChange={copy.setCopyName}
            onCancel={handleCancelCopyModal}
            onConfirm={handleConfirmCopyCustomization}
            isLoading={copy.isLoading}
          />

          <ExportCustomizationModal
            open={modals.exportModalOpen}
            onOpenChange={modals.setExportModalOpen}
            showParsedData={exportHook.showParsedData}
            exportCityId={exportHook.exportCityId}
            onExportCityIdChange={exportHook.setExportCityId}
            cities={exportHook.cities}
            selectedFile={exportHook.selectedFile}
            parsedData={exportHook.parsedData}
            isExporting={exportHook.isExporting}
            isSaving={exportHook.isSaving}
            onCancel={handleCloseExportModal}
            onConfirm={exportHook.showParsedData ? handleSaveImportedData : handleCloseExportModal}
            onDownloadExportFile={exportHook.handleDownloadExportFile}
            onUploadFile={exportHook.handleUploadFile}
          />

          <ImportCustomizationModal
            open={modals.importModalOpen}
            onOpenChange={modals.setImportModalOpen}
            showImportParsedData={importHook.showImportParsedData}
            importSelectedFile={importHook.importSelectedFile}
            importParsedData={importHook.importParsedData}
            isLoading={importHook.isLoading}
            onCancel={handleCloseImportModal}
            onConfirm={importHook.showImportParsedData ? handleSaveImportedCustomizations : handleCloseImportModal}
            onDownloadTemplate={importHook.handleDownloadTemplate}
            onImportFileUpload={importHook.handleImportFileUpload}
          />

          <input
            type='file'
            ref={exportHook.fileInputRef}
            onChange={exportHook.handleFileChange}
            accept='.xlsx,.xls'
            className='hidden'
          />
          <input
            type='file'
            ref={importHook.importFileInputRef}
            onChange={importHook.handleImportFileChange}
            accept='.xlsx,.xls'
            className='hidden'
          />
        </div>
      </Main>
    </>
  )
}
