import { z } from 'zod'

export const membershipTypeFormSchema = z.object({
  // HẠNG THÀNH VIÊN
  typeId: z.string().optional(),
  typeName: z.string().optional(),
  pointRate: z.number().optional(),
  isNoChange: z.boolean().optional(),

  // ĐIỀU KIỆN ĐẠT HẠNG
  upgradeAmount: z.string().optional(),
  moneyHandlingType: z.enum(['maintain', 'deduct', 'reset']).optional(),
  upgradeMinusPointAmount: z.string().optional(),
  upgradeResetPointAmount: z.string().optional(),

  // XÉT LẠI HẠNG THEO CHU KỲ
  isRankReviewEnabled: z.boolean().optional(),
  downgradeAmount: z.string().optional(),
  rankChangeType: z.enum(['maintain', 'downgrade']).optional(),
  downgradeToLevel: z.string().optional(),
  moneyChangeType: z.enum(['maintain', 'deduct', 'reset']).optional(),
  downgradeMinusPointAmount: z.number().optional(),
  downgradeResetPointAmount: z.number().optional(),
  downgradeMinusPoint: z.number().optional(),
  downgradeResetPoint: z.number().optional(),
  unchangeMinusPointAmount: z.number().optional(),
  unchangeResetPointAmount: z.number().optional(),
  pointChangeType: z.enum(['maintain', 'deduct', 'reset']).optional(),
  exceededMoneyChangeType: z.enum(['maintain', 'deduct', 'reset']).optional(),

  // Trạng thái
  isActive: z.boolean().optional()
})

export type MembershipTypeFormData = z.infer<typeof membershipTypeFormSchema>

export const defaultValues: MembershipTypeFormData = {
  typeId: '',
  typeName: '',
  pointRate: 0,
  isNoChange: false,
  upgradeAmount: '',
  moneyHandlingType: 'maintain',
  upgradeMinusPointAmount: '',
  upgradeResetPointAmount: '',
  isRankReviewEnabled: false,
  downgradeAmount: '',
  rankChangeType: 'maintain',
  downgradeToLevel: '',
  moneyChangeType: 'maintain',
  downgradeMinusPointAmount: 0,
  downgradeResetPointAmount: 0,
  downgradeMinusPoint: 0,
  downgradeResetPoint: 0,
  unchangeMinusPointAmount: 0,
  unchangeResetPointAmount: 0,
  pointChangeType: 'maintain',
  exceededMoneyChangeType: 'maintain',
  isActive: true
}
