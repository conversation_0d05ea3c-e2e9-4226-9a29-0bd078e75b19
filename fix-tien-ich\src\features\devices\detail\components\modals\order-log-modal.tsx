import { useState, useMemo } from 'react'

import { useOrderLogsData } from '@/hooks/api'

import { Dialog, DialogContent, DialogHeader, DialogTitle, Input } from '@/components/ui'

import { DateRangePicker } from '../date-range-picker'

interface OrderLogModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  deviceCode?: string
}

export function OrderLogModal({ open, onOpenChange, deviceCode }: OrderLogModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [startDate, setStartDate] = useState<Date>(() => {
    const today = new Date()
    return new Date(today.getFullYear(), today.getMonth(), 1)
  })
  const [endDate, setEndDate] = useState<Date>(() => {
    const today = new Date()
    return new Date(today.getFullYear(), today.getMonth() + 1, 0)
  })

  const datesValid = useMemo(() => {
    return startDate.getMonth() === endDate.getMonth() && startDate.getFullYear() === endDate.getFullYear()
  }, [startDate, endDate])

  const { startTimestamp, endTimestamp } = useMemo(() => {
    const start = startDate.getTime()
    const end = endDate.getTime() + 24 * 60 * 60 * 1000 - 1
    return { startTimestamp: start, endTimestamp: end }
  }, [startDate, endDate])

  const { data: orderLogsData, isLoading } = useOrderLogsData({
    device_code: deviceCode || '',
    start_date: startTimestamp,
    end_date: endTimestamp,
    search: searchTerm,
    enabled: open && !!deviceCode && datesValid
  })

  const transformedLogs = useMemo(() => {
    if (!orderLogsData) return []

    return orderLogsData.map((log: any) => ({
      id: log.tran_id,
      orderNumber: `#${log.change_data.tran_no || log.tran_id.slice(-6)}`,
      customerName: log.table_name || log.change_data.table_name || 'Không xác định',
      orderTime: new Date(log.change_data.start_date || log.start_date).toLocaleString('vi-VN'),
      status: log.change_data.sale_note || 'Hoàn thành',
      total: log.change_data.total_amount || 0,
      items:
        log.change_data.sale_detail?.map((item: any) => ({
          name: item.item_name,
          quantity: item.quantity,
          price: item.amount
        })) || [],
      subtotal: log.change_data.total_amount || 0,
      discount: log.change_data.extra_data?.deposit_amount || 0,
      finalTotal: log.change_data.total_amount || 0,
      customerCount: log.change_data.extra_data?.peo_count || 0,
      customerType: log.change_data.extra_data?.customer_name || '',
      phoneNumber: log.change_data.extra_data?.customer_phone || '',
      notes: log.change_data.extra_data?.note_deposit || log.change_data.sale_note || ''
    }))
  }, [orderLogsData])

  const filteredLogs = transformedLogs.filter(log => {
    const matchesSearch = log.orderNumber.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-4xl'>
        <DialogHeader>
          <DialogTitle className='text-center text-2xl font-medium'>Nhật ký order</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          {/* Search and Date Range */}
          <div className='flex gap-4'>
            <div className='flex-1'>
              <Input
                placeholder='Tìm kiếm theo mã hóa đơn'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
              />
            </div>
            <div className='w-64'>
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                onStartDateChange={date => date && setStartDate(date)}
                onEndDateChange={date => date && setEndDate(date)}
                placeholder='Chọn khoảng thời gian'
              />
            </div>
          </div>

          {/* Date validation warning */}
          {!datesValid && (
            <div className='rounded bg-red-50 p-2 text-sm text-red-600'>
              ⚠️ Ngày bắt đầu và ngày kết thúc phải cùng tháng
            </div>
          )}

          {/* Content - Simple List View */}
          <div className='max-h-96 overflow-y-auto'>
            {isLoading ? (
              <div className='py-8 text-center text-gray-500'>Đang tải dữ liệu...</div>
            ) : filteredLogs.length === 0 ? (
              <div className='py-8 text-center text-gray-500'>Không tìm thấy order nào</div>
            ) : (
              <div className='space-y-3'>
                {filteredLogs.map(log => (
                  <div key={log.id} className='rounded-lg border bg-white p-4'>
                    {/* Header */}
                    <div className='mb-3 flex items-center justify-between border-b pb-2'>
                      <div className='flex items-center gap-2'>
                        <span className='font-medium text-blue-600'>{log.orderNumber}</span>
                        <span className='text-sm text-gray-500'>
                          - Bàn 1 - 32131 - TN: CÔNG TY CỔ PHẦN TTMI - STT: 1
                        </span>
                      </div>
                      <div className='text-sm text-gray-600'>{log.orderTime}</div>
                    </div>

                    {/* Customer Info */}
                    <div className='mb-2 text-sm text-gray-600'>Ghi chú: 222222</div>

                    {/* Items */}
                    <div className='space-y-2'>
                      {log.items && log.items.length > 0 ? (
                        log.items.map((item: any, index: number) => (
                          <div key={index} className='flex items-center justify-between'>
                            <div className='flex items-center gap-2'>
                              <span className='text-sm font-medium'>{item.name}</span>
                              {item.time && <span className='text-xs text-gray-500'>⏰ {item.time}</span>}
                            </div>
                            <span className='font-medium'>{item.quantity}</span>
                          </div>
                        ))
                      ) : (
                        <div className='py-4 text-center text-sm text-gray-500'>Không có dữ liệu món ăn</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
