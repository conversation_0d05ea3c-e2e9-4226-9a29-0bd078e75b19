import { useState } from 'react'

import { Control, useWatch, useFormContext } from 'react-hook-form'

import { useChangePassword } from '@/hooks/api'

import { PasswordInput } from '@/components/password-input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage, Button } from '@/components/ui'

import type { CreateEmployeeFormData } from './schema/create-employee-form-schema'

interface PasswordSectionProps {
  control: Control<CreateEmployeeFormData>
  isEditMode: boolean
  userId?: string
}

export function PasswordSection({ control, isEditMode, userId }: PasswordSectionProps) {
  const [successMessage, setSuccessMessage] = useState('')
  const changePasswordMutation = useChangePassword()
  const { setValue } = useFormContext<CreateEmployeeFormData>()

  const password = useWatch({ control, name: 'password' })
  const confirmPassword = useWatch({ control, name: 'confirmPassword' })

  const canSavePassword = password && confirmPassword && password === confirmPassword && password.length >= 6

  const handleChangePassword = async () => {
    if (!userId || !canSavePassword) return

    try {
      setSuccessMessage('')
      await changePasswordMutation.mutateAsync({
        user_uid: userId,
        new_password: password,
        confirm_password: confirmPassword
      })
      setSuccessMessage('Đổi mật khẩu thành công')

      setValue('password', '')
      setValue('confirmPassword', '')
    } catch (error) {
      console.error('Change password error:', error)
    }
  }
  return (
    <div className='space-y-2'>
      <h3 className='text-base font-medium'>{isEditMode ? 'Đổi mật khẩu' : 'Tạo mật khẩu'}</h3>
      <p className='text-muted-foreground py-2 text-sm'>
        Vui lòng tạo một mật khẩu dài ít nhất 6 ký tự bao gồm chữ và số. Đổi mật khẩu sẽ làm tài khoản đăng xuất khỏi
        tất cả thiết bị
      </p>

      <FormField
        control={control}
        name='password'
        render={({ field }) => (
          <FormItem className='grid grid-cols-4 items-center gap-4'>
            <FormLabel className='text-right text-sm'>Mật khẩu *</FormLabel>
            <div className='col-span-3'>
              <FormControl>
                <PasswordInput placeholder={isEditMode ? 'Để trống nếu không đổi' : 'Nhập mật khẩu'} {...field} />
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name='confirmPassword'
        render={({ field }) => (
          <FormItem className='grid grid-cols-4 items-center gap-4'>
            <FormLabel className='text-right text-sm'>Xác nhận mật khẩu *</FormLabel>
            <div className='col-span-3'>
              <FormControl>
                <PasswordInput placeholder={isEditMode ? 'Để trống nếu không đổi' : 'Nhập lại mật khẩu'} {...field} />
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      />

      <div className='grid grid-cols-4 gap-2'>
        <div></div>
        <div className='col-span-4'>
          {password && confirmPassword && password !== confirmPassword && (
            <p className='text-sm text-red-600'>Mật khẩu không khớp</p>
          )}
          {password && password.length > 0 && password.length < 6 && (
            <p className='text-sm text-red-600'>Mật khẩu phải chứa ít nhất 6 ký tự bao gồm chữ và số</p>
          )}
          {successMessage && <p className='text-sm text-green-600'>{successMessage}</p>}
        </div>
      </div>

      {isEditMode && userId && (
        <div className='grid grid-cols-4 gap-2'>
          <div></div>
          <div className='col-span-4'>
            <Button
              type='button'
              onClick={handleChangePassword}
              disabled={!canSavePassword}
              className={`${
                !canSavePassword
                  ? 'cursor-not-allowed bg-gray-300 text-gray-500 hover:bg-gray-300'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {changePasswordMutation.isPending ? 'Đang lưu...' : 'Lưu mật khẩu mới'}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
