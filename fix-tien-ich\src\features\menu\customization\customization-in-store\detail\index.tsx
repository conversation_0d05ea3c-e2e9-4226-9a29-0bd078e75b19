import { useState } from 'react'

import { X } from 'lucide-react'

import { usePosStores } from '@/stores/posStore'

import { useItemsData } from '@/hooks/api'

import {
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Input
} from '@/components/ui'

import { CreateGroupModal, AddMenuItemModal, ItemSelectionModal } from './components'
import {
  useCustomizationForm,
  useGroupManagement,
  useMenuItemSelection,
  useModalState
} from './hooks'

export default function CustomizationInStoreDetailPage() {
  const customizationForm = useCustomizationForm()
  const groupManagement = useGroupManagement()
  const menuItemSelection = useMenuItemSelection({
    onConfirm: newItems => groupManagement.setMenuItems(newItems)
  })
  const modalState = useModalState()

  const [showItemModal, setShowItemModal] = useState(false)
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())

  const { currentBrandStores } = usePosStores()

  const { data: items = [], isLoading: isLoadingItems } = useItemsData({
    params: {
      list_store_uid: customizationForm.selectedStoreId,
      skip_limit: true,
      active: 1
    },
    enabled: !!customizationForm.selectedStoreId
  })

  const handleCreateGroup = () => {
    groupManagement.handleCreateGroup()
    modalState.setCreateGroupModalOpen(true)
  }

  const handleEditGroup = (groupId: string) => {
    groupManagement.handleEditGroup(groupId)
    modalState.setCreateGroupModalOpen(true)
  }

  const handleCloseModal = () => {
    modalState.handleCloseModal()
    groupManagement.resetGroupForm()
    menuItemSelection.resetSelection()
  }

  const handleCloseAddItemModal = () => {
    modalState.handleCloseAddItemModal()
    menuItemSelection.resetSelection()
  }

  const handleConfirmMenuItems = () => {
    menuItemSelection.handleConfirmMenuItems(items)
    modalState.handleCloseAddItemModal()
  }

  const handleAddMenuItem = () => {
    menuItemSelection.setSelectedMenuItemsFromGroup(groupManagement.menuItems)
    modalState.handleAddMenuItem(customizationForm.selectedStoreId)
  }

  const handleSaveGroup = () => {
    if (groupManagement.handleSaveGroup(items)) {
      handleCloseModal()
    }
  }

  const handleSave = async () => {
    await customizationForm.handleSave(groupManagement.customizationGroups, selectedItems, items)
  }

  const handleItemSelection = () => {
    setShowItemModal(true)
  }

  const handleItemsSelected = (selectedItemIds: string[]) => {
    setSelectedItems(new Set(selectedItemIds))
    setShowItemModal(false)
  }

  const getSelectedItemsDisplay = () => {
    if (selectedItems.size === 0) {
      return 'Chọn món áp dụng'
    }
    return `${selectedItems.size} món`
  }

  const selectedMenuItemsList = menuItemSelection.getSelectedMenuItemsList(items)
  const remainingMenuItemsList = menuItemSelection.getRemainingMenuItemsList(items)

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button
            variant='ghost'
            size='sm'
            onClick={customizationForm.handleBack}
            className='flex items-center'
          >
            <X className='h-4 w-4' />
          </Button>
          <Button
            type='button'
            disabled={customizationForm.isSubmitting || !customizationForm.isFormValid}
            className='min-w-[100px]'
            onClick={handleSave}
          >
            {customizationForm.isSubmitting ? 'Đang tạo...' : 'Lưu'}
          </Button>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>Tạo customization</h1>
        </div>
      </div>

      <div className='mx-auto max-w-4xl'>
        <div className='p-6'>
          <div className='space-y-6'>
            <div className='flex items-center gap-4'>
              <Label htmlFor='customization-name' className='min-w-[200px] text-sm font-medium'>
                Tên customization <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='customization-name'
                value={customizationForm.customizationName}
                onChange={e => customizationForm.setCustomizationName(e.target.value)}
                placeholder='Nhập tên customization'
                className='flex-1'
              />
            </div>

            <div className='flex items-center gap-4'>
              <Label htmlFor='store-select' className='min-w-[200px] text-sm font-medium'>
                Cửa hàng <span className='text-red-500'>*</span>
              </Label>
              <Select
                value={customizationForm.selectedStoreId}
                onValueChange={customizationForm.setSelectedStoreId}
              >
                <SelectTrigger className='flex-1'>
                  <SelectValue placeholder='Chọn cửa hàng' />
                </SelectTrigger>
                <SelectContent>
                  {currentBrandStores.map(store => (
                    <SelectItem key={store.id} value={store.id}>
                      {store.store_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Item Assignment Section */}
            <div className='space-y-4'>
              <div>
                <h3 className='text-lg font-medium text-gray-900'>Áp dụng customization cho món</h3>
                <p className='text-sm text-gray-600'>
                  Chọn các món mà customization này sẽ áp dụng
                </p>
              </div>

              {/* Áp dụng cho món */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Món ăn</Label>
                <div className='flex-1'>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={handleItemSelection}
                    className='w-full justify-start text-left'
                    disabled={isLoadingItems}
                  >
                    {isLoadingItems ? 'Đang tải...' : getSelectedItemsDisplay()}
                  </Button>
                </div>
              </div>
            </div>

            <div className='flex justify-center pt-4'>
              <Button onClick={handleCreateGroup}>Tạo nhóm</Button>
            </div>

            {groupManagement.customizationGroups.length > 0 && (
              <div className='space-y-6 pt-6'>
                <h3 className='text-lg font-medium'>Danh sách nhóm đã tạo</h3>
                {groupManagement.customizationGroups.map(group => (
                  <div key={group.id} className='space-y-3'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <span className='font-medium'>{group.name}</span>
                        <span className='ml-2 text-sm text-gray-500'>
                          (Chọn từ {group.minRequired} đến {group.maxAllowed} món)
                        </span>
                      </div>
                      <div className='flex gap-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleEditGroup(group.id)}
                        >
                          Sửa
                        </Button>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => groupManagement.handleDeleteGroup(group.id)}
                          className='text-red-600 hover:text-red-700'
                        >
                          Xóa
                        </Button>
                      </div>
                    </div>

                    <div className='grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4'>
                      {group.items.map(item => (
                        <div key={item.id} className='rounded-md border bg-gray-50 p-3 text-center'>
                          <p className='text-sm font-medium'>{item.name}</p>
                          <p className='mt-1 text-xs text-gray-500'>({item.code})</p>
                          <p className='mt-1 text-sm font-medium text-green-600'>
                            {item.price.toLocaleString('vi-VN')} ₫
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <CreateGroupModal
        open={modalState.createGroupModalOpen}
        onOpenChange={modalState.setCreateGroupModalOpen}
        onCancel={handleCloseModal}
        onConfirm={handleSaveGroup}
        onAddMenuItem={handleAddMenuItem}
        isEditing={groupManagement.isEditing}
        customizationName={customizationForm.customizationName}
        groupName={groupManagement.groupName}
        setGroupName={groupManagement.setGroupName}
        minRequired={groupManagement.minRequired}
        setMinRequired={groupManagement.setMinRequired}
        maxAllowed={groupManagement.maxAllowed}
        setMaxAllowed={groupManagement.setMaxAllowed}
        menuItems={groupManagement.menuItems}
      />

      <AddMenuItemModal
        open={modalState.addItemModalOpen}
        onOpenChange={modalState.setAddItemModalOpen}
        onCancel={handleCloseAddItemModal}
        onConfirm={handleConfirmMenuItems}
        menuItemSearchTerm={menuItemSelection.menuItemSearchTerm}
        setMenuItemSearchTerm={menuItemSelection.setMenuItemSearchTerm}
        selectedMenuSectionOpen={menuItemSelection.selectedMenuSectionOpen}
        setSelectedMenuSectionOpen={menuItemSelection.setSelectedMenuSectionOpen}
        remainingMenuSectionOpen={menuItemSelection.remainingMenuSectionOpen}
        setRemainingMenuSectionOpen={menuItemSelection.setRemainingMenuSectionOpen}
        selectedMenuItems={menuItemSelection.selectedMenuItems}
        handleMenuItemToggle={menuItemSelection.handleMenuItemToggle}
        selectedMenuItemsList={selectedMenuItemsList}
        remainingMenuItemsList={remainingMenuItemsList}
      />

      <ItemSelectionModal
        open={showItemModal}
        onOpenChange={setShowItemModal}
        items={items}
        selectedItems={Array.from(selectedItems)}
        onItemsSelected={handleItemsSelected}
      />
    </div>
  )
}
