import { createContext, useContext, ReactNode, useMemo, useState } from 'react'

import type { ExtraPointItem, MembershipTypeItem } from '@/types/api/crm'

import { useExtraPoint, useMembershipType, useUpdateExtraPoint } from '@/hooks/crm'

interface ExtraPointWithCalculation extends ExtraPointItem {
  calculatedRate: number
  membershipTypeName: string
}

interface ExtraPointContextValue {
  data: ExtraPointWithCalculation[]
  membershipTypes: MembershipTypeItem[]
  isLoading: boolean
  error: Error | null
  refetch: () => void
  toggleActivation: (id: string, currentActive: number) => void
  createDialogOpen: boolean
  setCreateDialogOpen: (open: boolean) => void
  editDialogOpen: boolean
  setEditDialogOpen: (open: boolean) => void
  selectedExtraPoint: ExtraPointItem | null
  setSelectedExtraPoint: (item: ExtraPointItem | null) => void
}

const ExtraPointContext = createContext<ExtraPointContextValue | undefined>(undefined)

interface ExtraPointProviderProps {
  children: ReactNode
}

export function ExtraPointProvider({ children }: ExtraPointProviderProps) {
  const extraPointQuery = useExtraPoint()
  const membershipTypeQuery = useMembershipType()
  const updateExtraPointMutation = useUpdateExtraPoint()
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedExtraPoint, setSelectedExtraPoint] = useState<ExtraPointItem | null>(null)

  const dataWithCalculation = useMemo(() => {
    const extraPoints = extraPointQuery.data?.list_membership_type_extra_rate || []
    const membershipTypes = membershipTypeQuery.data?.list_membership_type || []

    return extraPoints.map(extraPoint => {
      const membershipType = membershipTypes.find(mt => mt.type_id === extraPoint.type_id)
      const pointRate = membershipType?.point_rate || 0
      const calculatedRate = pointRate * extraPoint.extra_rate * 100 // Convert to percentage

      return {
        ...extraPoint,
        calculatedRate,
        membershipTypeName: membershipType?.type_name || extraPoint.type_name || 'N/A'
      }
    })
  }, [extraPointQuery.data, membershipTypeQuery.data])

  const toggleActivation = (id: string, currentActive: number) => {
    const extraPoints = extraPointQuery.data?.list_membership_type_extra_rate || []
    const item = extraPoints.find(ep => ep.id === id)

    if (!item) {
      console.error('Extra point item not found:', id)
      return
    }

    const updatedItem = {
      ...item,
      active: currentActive === 1 ? 0 : 1, // Toggle active status
      updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19),
      class: 'membership-feature',
      title: currentActive === 1 ? 'Đã hủy' : 'Đang hoạt động',
      action: currentActive === 1 ? 'Kích hoạt' : 'Hủy'
    }

    updateExtraPointMutation.mutate(updatedItem)
  }

  const value: ExtraPointContextValue = {
    data: dataWithCalculation,
    membershipTypes: membershipTypeQuery.data?.list_membership_type || [],
    isLoading: extraPointQuery.isLoading || membershipTypeQuery.isLoading,
    error: extraPointQuery.error || membershipTypeQuery.error,
    refetch: () => {
      extraPointQuery.refetch()
      membershipTypeQuery.refetch()
    },
    toggleActivation,
    createDialogOpen,
    setCreateDialogOpen,
    editDialogOpen,
    setEditDialogOpen,
    selectedExtraPoint,
    setSelectedExtraPoint
  }

  return <ExtraPointContext.Provider value={value}>{children}</ExtraPointContext.Provider>
}

export function useExtraPointContext() {
  const context = useContext(ExtraPointContext)
  if (context === undefined) {
    throw new Error('useExtraPointContext must be used within an ExtraPointProvider')
  }
  return context
}
