import { useMemo, useState, useEffect } from 'react'

import { ChevronDown } from 'lucide-react'

import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

import { Card, CardAction, CardContent, CardHeader, CardTitle } from '@/components/ui'

import { useDashboardContext } from '../../context/dashboard-context'

export function ProfitLossReportChart() {
  const { ktvReportData, isKtvReportLoading, ktvReportError, setKtvTimestamp } = useDashboardContext()

  const [selectedMonth, setSelectedMonth] = useState(8)
  const [selectedYear, setSelectedYear] = useState(2025)

  const currentMonth = useMemo(() => {
    return `Tháng ${selectedMonth}/${selectedYear}`
  }, [selectedMonth, selectedYear])

  const selectedTimestamp = useMemo(() => {
    const date = new Date(selectedYear, selectedMonth - 1, 1)
    return date.getTime()
  }, [selectedMonth, selectedYear])

  useEffect(() => {
    setKtvTimestamp(selectedTimestamp)
  }, [selectedTimestamp, setKtvTimestamp])

  const monthOptions = useMemo(() => {
    const currentDate = new Date()
    const currentMonth = currentDate.getMonth() + 1
    const currentYear = currentDate.getFullYear()

    return Array.from({ length: 12 }, (_, i) => {
      const monthValue = i + 1
      const isDisabled = selectedYear === currentYear && monthValue > currentMonth

      return {
        value: monthValue,
        label: `Tháng ${monthValue}`,
        disabled: isDisabled
      }
    })
  }, [selectedYear])

  // Generate detail data from KTV API response
  const detailData = useMemo(
    () => [
      {
        label: 'Chi phí nguyên vật liệu',
        subtitle: 'Bạn có',
        highlightNumber: ktvReportData?.count_item_not_price?.toString() || '0',
        subtitleEnd: 'món chưa có giá thành phẩm',
        amount: ktvReportData?.material_cost || 0,
        percentage: ktvReportData?.percentage_material ? `${ktvReportData.percentage_material} %` : '0 %'
      },
      {
        label: 'Chi phí trong tháng',
        subtitle: 'Bạn có',
        highlightNumber: `${ktvReportData?.count_cost_out || 0}/ ${ktvReportData?.count_cost_out_all || 0}`,
        subtitleEnd: 'mục chưa khai báo chi phí',
        amount: ktvReportData?.cost_amount_inex || 0,
        percentage: ktvReportData?.percentage_inex ? `${ktvReportData.percentage_inex} %` : '0 %'
      },
      {
        label: 'Chi phí phân bổ hàng tháng',
        subtitle: 'Bạn đang có',
        highlightNumber: ktvReportData?.count_tool?.toString() || '0',
        subtitleEnd: 'khoản phân bổ',
        amount: ktvReportData?.tool_cost || 0,
        percentage: ktvReportData?.percentage_tool ? `${ktvReportData.percentage_tool} %` : '0 %'
      }
    ],
    [ktvReportData]
  )

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount) + ' ₫'
  }

  // Get revenue from KTV data - no fallback defaults
  const revenueAmount = ktvReportData?.revenue_net || 0
  const profitAmount = ktvReportData?.profit || 0

  // Calculate total costs from KTV data
  const totalCosts = useMemo(() => {
    if (!ktvReportData) return 0
    return (ktvReportData.material_cost || 0) + (ktvReportData.tool_cost || 0) + (ktvReportData.cost_amount_inex || 0)
  }, [ktvReportData])

  if (isKtvReportLoading) {
    return (
      <Card className='col-span-1'>
        <CardHeader>
          <CardTitle>Báo cáo lãi lỗ tháng</CardTitle>
        </CardHeader>
        <CardContent className='flex h-[300px] items-center justify-center'>
          <div className='text-muted-foreground text-sm'>Đang tải dữ liệu...</div>
        </CardContent>
      </Card>
    )
  }

  if (ktvReportError) {
    return (
      <Card className='col-span-1'>
        <CardHeader>
          <CardTitle>Báo cáo lãi lỗ tháng</CardTitle>
        </CardHeader>
        <CardContent className='flex h-[300px] items-center justify-center'>
          <div className='text-sm text-red-500'>Lỗi: {ktvReportError}</div>
        </CardContent>
      </Card>
    )
  }

  // Check if no data available
  if (!ktvReportData || (revenueAmount === 0 && profitAmount === 0)) {
    return (
      <Card className='col-span-1'>
        <CardHeader>
          <CardTitle>Báo cáo lãi lỗ tháng</CardTitle>
        </CardHeader>
        <CardContent className='flex h-[300px] items-center justify-center'>
          <div className='text-muted-foreground text-sm'>Chưa có thông tin</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className='col-span-1'>
      <CardHeader>
        <CardTitle>Báo cáo lãi lỗ tháng</CardTitle>
        <div className='mt-2'>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className='flex items-center border border-gray-200 px-3 py-1 text-xs text-gray-900 hover:bg-gray-50'>
                {currentMonth}
                <ChevronDown className='ml-2 h-3 w-3' />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='start' className='w-48'>
              <div className='p-2'>
                <div className='mb-2'>
                  <label className='mb-1 block text-xs font-medium text-gray-700'>Tháng</label>
                  <div className='grid grid-cols-3 gap-1'>
                    {monthOptions.map(month => (
                      <button
                        key={month.value}
                        onClick={() => !month.disabled && setSelectedMonth(month.value)}
                        disabled={month.disabled}
                        className={`rounded px-2 py-1 text-xs ${
                          month.disabled
                            ? 'cursor-not-allowed bg-gray-100 text-gray-400'
                            : selectedMonth === month.value
                              ? 'bg-blue-100 text-blue-700'
                              : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        {month.value}
                      </button>
                    ))}
                  </div>
                </div>
                <div>
                  <label className='mb-1 block text-xs font-medium text-gray-700'>Năm</label>
                  <input
                    type='number'
                    value={selectedYear}
                    onChange={e => setSelectedYear(parseInt(e.target.value) || 2025)}
                    className='w-full rounded border border-gray-300 px-2 py-1 text-xs focus:border-blue-500 focus:outline-none'
                    min='2020'
                    max='2030'
                  />
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardAction>
          {/* <button className='cursor-pointer text-xs text-blue-600 hover:text-blue-800'>Chi tiết</button> */}
        </CardAction>
      </CardHeader>
      <CardContent className='space-y-4'>
        {/* Revenue Header */}
        <div className='flex items-center justify-between pb-2'>
          <span className='text-xs font-semibold text-gray-900'>Doanh thu</span>
          <span className='text-xs font-semibold text-gray-900'>{formatCurrency(revenueAmount)}</span>
        </div>

        {/* Detail Table */}
        <div className='space-y-2'>
          {/* Table Headers */}
          <div className='flex items-center justify-between border-b-2 border-gray-200 pb-2'>
            <span className='text-xs font-semibold text-gray-900'>Tổng chi phí</span>
            <span className='text-xs font-semibold text-gray-900'>{formatCurrency(totalCosts)}</span>
          </div>

          {/* Table Rows */}
          {detailData.map((item, index) => (
            <div key={index} className='flex items-start justify-between border-b border-gray-100 py-3 last:border-b-0'>
              <div className='flex-1'>
                <div className='text-xs font-medium text-gray-900'>{item.label}</div>
                <div className='mt-1 text-xs text-gray-500'>
                  {item.subtitle} <span className='font-bold text-red-600'>{item.highlightNumber}</span>{' '}
                  {item.subtitleEnd}
                </div>
              </div>
              <div className='text-right'>
                <div className={`text-xs ${item.amount === 0 ? 'text-gray-500' : 'font-medium'}`}>
                  {formatCurrency(item.amount)}
                </div>
                <div className='mt-1 text-xs font-bold text-red-600'>{item.percentage}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Profit Section */}
        <div className='mt-4 border-t border-gray-200 pt-4'>
          <div className='flex items-start justify-between py-3'>
            <div className='flex-1'>
              <div className='text-xs font-medium text-gray-900'>Lợi nhuận</div>
            </div>
            <div className='text-right'>
              <div className='text-xs font-bold text-black'>{formatCurrency(profitAmount)}</div>
              <div className='mt-1 text-xs font-bold text-red-600'>
                {ktvReportData?.percentage_profit ? `${ktvReportData.percentage_profit} %` : '0 %'}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
