import { useState, useMemo } from 'react'

import {
  ColumnFiltersState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  useReactTable
} from '@tanstack/react-table'

import { useBillingServices } from '@/hooks/crm'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui'

import { TableSkeleton } from '@/features/order-sources/components/data-tables/table-skeleton'

import { createPricingTableColumns } from './pricing-table-columns'

export function PricingTable() {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})

  const { services, isLoading, error } = useBillingServices()

  const filteredServices = services.filter(service => service.sort !== 1000)

  const columns = useMemo(() => createPricingTableColumns(), [])

  const table = useReactTable({
    data: filteredServices,
    columns,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnFilters,
      columnVisibility,
      rowSelection
    }
  })

  if (error) {
    return (
      <div className='flex flex-col items-center justify-center space-y-4 py-8'>
        <div className='text-center'>
          <h3 className='text-destructive text-lg font-semibold'>Lỗi khi tải dữ liệu</h3>
          <p className='text-muted-foreground text-sm'>{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-4'>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading && <TableSkeleton />}
            {!isLoading &&
              table.getRowModel().rows?.length &&
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))}
            {!isLoading && !table.getRowModel().rows?.length && (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  Không có dữ liệu
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
