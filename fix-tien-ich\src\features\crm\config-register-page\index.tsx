import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui'

import { ConfigRegisterForm, RegistrationPageLinkSection } from './components'
import { ConfigRegisterProvider } from './context'

function Page() {
  return (
    <div className='space-y-6'>
      <h1 className='text-2xl font-semibold tracking-tight'>Trang đăng ký thành viên</h1>

      <Card>
        <CardHeader className='pb-4'>
          <CardTitle className='text-base'>Đường dẫn trang đăng ký</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <RegistrationPageLinkSection />
          <ConfigRegisterForm />
        </CardContent>
      </Card>
    </div>
  )
}

export default function CrmConfigRegisterPage() {
  return (
    <ConfigRegisterProvider>
      <Page />
    </ConfigRegisterProvider>
  )
}
