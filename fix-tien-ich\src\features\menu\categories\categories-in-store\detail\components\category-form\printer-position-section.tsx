import type { CreatedItemCategory } from '@/lib/item-categories-api'

import { Button, Label } from '@/components/ui'

interface PrinterPositionSectionProps {
  showPrinterSection: boolean
  selectedPrinters: string[]
  onPrinterSelection: () => void
  getSelectedPrintersDisplay: () => string
  newlyCreatedCategory?: CreatedItemCategory | null
}

export function PrinterPositionSection({
  showPrinterSection,
  selectedPrinters: _selectedPrinters,
  onPrinterSelection,
  getSelectedPrintersDisplay,
  newlyCreatedCategory: _newlyCreatedCategory
}: PrinterPositionSectionProps) {
  if (!showPrinterSection) {
    return null
  }

  return (
    <div className='space-y-4'>
      <div>
        <h3 className='text-lg font-medium text-gray-900'>Vị trí máy in áp dụng</h3>
        <p className='text-sm text-gray-600'>Chọn các vị trí máy in sẽ áp dụng cho nhóm món này.</p>
      </div>

      {/* Chọn vị trí máy in */}
      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>Chọn vị trí máy in</Label>
        <div className='flex-1'>
          <Button
            type='button'
            variant='outline'
            onClick={onPrinterSelection}
            className='w-full justify-start text-left'
          >
            {getSelectedPrintersDisplay()}
          </Button>
        </div>
      </div>
    </div>
  )
}
