import { ColumnDef } from '@tanstack/react-table'

import type { CrmService } from '@/types/api/crm'

import { DataTableColumnHeader } from '@/components/data-table'

export const createPricingTableColumns = (): ColumnDef<CrmService>[] => [
  {
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Loại chi phí' />,
    cell: ({ row }) => (
      <div className='text-left break-words whitespace-normal'>
        <div className='font-medium'>{row.getValue('name')}</div>
        <div className='text-muted-foreground mt-1 text-sm leading-relaxed'>{row.original.desc}</div>
      </div>
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'unitName',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Đơn vị' />,
    cell: ({ row }) => <div className='text-center'>{row.getValue('unitName')}</div>,
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'unitPrice',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Giá (VNĐ)' />,
    cell: ({ row }) => {
      const price = row.getValue('unitPrice') as number
      return <div className='text-center font-medium'>{price.toLocaleString('vi-VN')} VNĐ</div>
    },
    enableSorting: false,
    enableHiding: false
  }
]
