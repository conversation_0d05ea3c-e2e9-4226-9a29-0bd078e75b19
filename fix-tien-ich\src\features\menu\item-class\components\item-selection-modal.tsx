import React, { useState, useMemo } from 'react'

import { ChevronDown, ChevronRight } from 'lucide-react'

import type { Item } from '@/lib/item-api'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'

interface ItemSelectionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  items: Item[]
  selectedItems: string[]
  onItemsSelected: (selectedItemIds: string[]) => void
}

export function ItemSelectionModal({
  open,
  onOpenChange,
  items,
  selectedItems,
  onItemsSelected
}: ItemSelectionModalProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCollapsed, setSelectedCollapsed] = useState(false)
  const [remainingCollapsed, setRemainingCollapsed] = useState(false)
  const [localSelectedItems, setLocalSelectedItems] = useState<string[]>(selectedItems)

  // Debug logging
  console.log('ItemSelectionModal - selectedItems:', selectedItems)
  console.log('ItemSelectionModal - open:', open)

  // Filter items based on search query
  const filteredItems = useMemo(() => {
    if (!searchQuery.trim()) return items

    const searchLower = searchQuery.toLowerCase()
    return items.filter(
      item =>
        item.item_name.toLowerCase().includes(searchLower) ||
        item.item_id.toLowerCase().includes(searchLower)
    )
  }, [items, searchQuery])

  // Separate selected and remaining items
  const selectedItemsData = filteredItems.filter(item => localSelectedItems.includes(item.item_id))
  const remainingItems = filteredItems.filter(item => !localSelectedItems.includes(item.item_id))

  const handleItemToggle = (itemId: string) => {
    const newSelectedItems = localSelectedItems.includes(itemId)
      ? localSelectedItems.filter(id => id !== itemId)
      : [...localSelectedItems, itemId]

    setLocalSelectedItems(newSelectedItems)
  }

  const handleSave = () => {
    onItemsSelected(localSelectedItems)
    onOpenChange(false)
  }

  const handleCancel = () => {
    setLocalSelectedItems(selectedItems)
    onOpenChange(false)
  }

  // Reset local selection when modal opens
  React.useEffect(() => {
    if (open) {
      setLocalSelectedItems(selectedItems)
      setSearchQuery('')
    }
  }, [open, selectedItems])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-md'>
        <div className='space-y-4'>
          {/* Search Input */}
          <div>
            <Input
              placeholder='Tìm kiếm món'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className='w-full'
            />
          </div>

          {/* Selected Items Collapse */}
          <Collapsible open={!selectedCollapsed} onOpenChange={setSelectedCollapsed}>
            <CollapsibleTrigger asChild>
              <Button
                variant='ghost'
                className='flex w-full items-center justify-between p-2 text-left'
              >
                <span>Đã chọn ({selectedItemsData.length})</span>
                {selectedCollapsed ? (
                  <ChevronRight className='h-4 w-4' />
                ) : (
                  <ChevronDown className='h-4 w-4' />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className='space-y-2'>
              {selectedItemsData.map(item => (
                <div key={item.id} className='flex items-center space-x-2 p-2'>
                  <Checkbox checked={true} onCheckedChange={() => handleItemToggle(item.item_id)} />
                  <span className='text-sm'>{item.item_name}</span>
                </div>
              ))}
              {selectedItemsData.length === 0 && (
                <div className='p-2 text-sm text-gray-500'>Chưa chọn món nào</div>
              )}
            </CollapsibleContent>
          </Collapsible>

          {/* Remaining Items Collapse */}
          <Collapsible open={!remainingCollapsed} onOpenChange={setRemainingCollapsed}>
            <CollapsibleTrigger asChild>
              <Button
                variant='ghost'
                className='flex w-full items-center justify-between p-2 text-left'
              >
                <span>Còn lại ({remainingItems.length})</span>
                {remainingCollapsed ? (
                  <ChevronRight className='h-4 w-4' />
                ) : (
                  <ChevronDown className='h-4 w-4' />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className='space-y-2'>
              {remainingItems.map(item => (
                <div key={item.id} className='flex items-center space-x-2 p-2'>
                  <Checkbox
                    checked={false}
                    onCheckedChange={() => handleItemToggle(item.item_id)}
                  />
                  <span className='text-sm'>{item.item_name}</span>
                </div>
              ))}
              {remainingItems.length === 0 && (
                <div className='p-2 text-sm text-gray-500'>Không có món nào</div>
              )}
            </CollapsibleContent>
          </Collapsible>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel}>
            Hủy
          </Button>
          <Button onClick={handleSave}>Lưu</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
