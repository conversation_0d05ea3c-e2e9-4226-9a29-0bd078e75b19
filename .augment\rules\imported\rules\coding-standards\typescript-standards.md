---
type: "always_apply"
---

# TypeScript Standards

## Interface Definitions
- **Clear, descriptive interfaces** with meaningful property names
- **Extend base interfaces** when appropriate to maintain consistency
- **Use proper typing** for all props and data structures
- **Avoid `any` type** - use specific types or unions instead

## Hook Patterns
- **Focused hooks** with clear return types and single responsibility
- **Consistent naming** following `use[Domain][Action]` convention
- **Return objects** with descriptive property names
