import { createContext, useContext, ReactNode, useState } from 'react'

import type { MembershipTypeItem } from '@/types/api/crm'

import {
  useMembershipType,
  useCreateMembershipTypeMutation,
  useUpdateMembershipTypeMutation
} from '@/hooks/crm/use-membership-type'

interface MembershipTypeContextValue {
  data: MembershipTypeItem[]
  isLoading: boolean
  error: Error | null
  refetch: () => void
  toggleActivation: (id: string, currentActive: number) => void
  createDialogOpen: boolean
  setCreateDialogOpen: (open: boolean) => void
  editDialogOpen: boolean
  setEditDialogOpen: (open: boolean) => void
  selectedMembershipType: MembershipTypeItem | null
  setSelectedMembershipType: (item: MembershipTypeItem | null) => void
  updateMembershipTypeMutation: ReturnType<typeof useUpdateMembershipTypeMutation>
  createMembershipTypeMutation: ReturnType<typeof useCreateMembershipTypeMutation>
}
const MembershipTypeContext = createContext<MembershipTypeContextValue | undefined>(undefined)

interface MembershipTypeProviderProps {
  children: ReactNode
}

export function MembershipTypeProvider({ children }: MembershipTypeProviderProps) {
  const membershipTypeQuery = useMembershipType()
  const updateMembershipTypeMutation = useUpdateMembershipTypeMutation()
  const createMembershipTypeMutation = useCreateMembershipTypeMutation()
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedMembershipType, setSelectedMembershipType] = useState<MembershipTypeItem | null>(null)

  const toggleActivation = async (id: string, currentActive: number) => {
    const membershipTypes = membershipTypeQuery.data?.list_membership_type || []
    const item = membershipTypes.find(mt => mt.type_id === id)

    if (!item) {
      return
    }

    const updatedItem = {
      ...item,
      active: currentActive === 1 ? 0 : 1,
      updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
    }

    await updateMembershipTypeMutation.mutateAsync(updatedItem)
  }

  const value: MembershipTypeContextValue = {
    data: membershipTypeQuery.data?.list_membership_type || [],
    isLoading: membershipTypeQuery.isLoading,
    error: membershipTypeQuery.error,
    refetch: membershipTypeQuery.refetch,
    toggleActivation,
    createDialogOpen,
    setCreateDialogOpen,
    editDialogOpen,
    setEditDialogOpen,
    selectedMembershipType,
    setSelectedMembershipType,
    updateMembershipTypeMutation,
    createMembershipTypeMutation
  }

  return <MembershipTypeContext.Provider value={value}>{children}</MembershipTypeContext.Provider>
}

export function useMembershipTypeContext() {
  const context = useContext(MembershipTypeContext)
  if (context === undefined) {
    throw new Error('useMembershipTypeContext must be used within a MembershipTypeProvider')
  }
  return context
}
