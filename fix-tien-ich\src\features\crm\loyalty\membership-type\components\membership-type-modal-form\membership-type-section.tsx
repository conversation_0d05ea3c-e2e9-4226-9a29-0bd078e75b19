import { UseFormReturn } from 'react-hook-form'

import { Checkbox, FormField, FormItem, FormLabel, FormControl, FormMessage, Input } from '@/components/ui'

import type { MembershipTypeFormData } from './schema'

interface MembershipTypeSectionProps {
  form: UseFormReturn<MembershipTypeFormData>

  editMode?: boolean
}

export function MembershipTypeSection({ form, editMode }: MembershipTypeSectionProps) {
  const { watch } = form
  const pointRate = watch('pointRate')

  const isPointRateInvalid = pointRate && pointRate > 100

  return (
    <div className='space-y-6'>
      <h3 className='text-lg font-semibold text-gray-900'>HẠNG THÀNH VIÊN</h3>

      <div className='grid grid-cols-1 gap-4'>
        <FormField
          control={form.control}
          name='typeId'
          render={({ field }) => (
            <FormItem>
              <FormLabel>ID *</FormLabel>
              <FormControl>
                <Input placeholder='Nhập ID hạng thành viên' disabled={editMode} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='typeName'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Hạng thành viên *</FormLabel>
              <FormControl>
                <Input placeholder='Nhập tên hạng thành viên' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tích điểm */}
        <FormField
          control={form.control}
          name='pointRate'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tích điểm *</FormLabel>
              <FormControl>
                <div className='flex items-center gap-3'>
                  <Input
                    placeholder='0.01'
                    type='number'
                    value={field.value || ''}
                    onChange={e => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                    className={`w-32 ${isPointRateInvalid ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                  />
                  <span className='rounded bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700'>
                    % giá trị hóa đơn
                  </span>
                  <a
                    href='/crm/marketing/create-evoucher'
                    target='_blank'
                    rel='noopener noreferrer'
                    className='ring-offset-background focus-visible:ring-ring inline-flex h-9 items-center justify-center rounded-md border border-orange-200 bg-orange-50 px-3 text-sm font-medium text-orange-600 transition-colors hover:bg-orange-100 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50'
                  >
                    Tạo voucher đổi điểm
                  </a>
                </div>
              </FormControl>
              <div className='text-sm'>
                <a href='#' className='text-blue-600 hover:underline'>
                  Cài đặt làm tròn điểm tích lũy tại đây
                </a>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Hạng không đổi */}
        <FormField
          control={form.control}
          name='isNoChange'
          render={({ field }) => (
            <FormItem className='space-y-3'>
              <div className='flex items-center space-x-2'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <FormLabel>Hạng không đổi</FormLabel>
              </div>
              <div className='rounded bg-gray-50 p-3 text-sm text-gray-600'>
                Hạng không đổi sẽ không ảnh hưởng bởi các điều kiện hạng thành viên thông thường, không xét lại theo chu
                kỳ
              </div>
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
