import { RemovedItem, CityData } from '@/types/item-removed'
import ExcelJS from 'exceljs'

function formatVietnameseCurrency(amount: number): string {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

function formatVietnameseDateTime(timestamp: number): string {
  const date = new Date(timestamp * 1000)
  const dateStr = date.toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
  const timeStr = date.toLocaleTimeString('vi-VN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })
  return `${dateStr} ${timeStr}`
}

export async function exportRemovedItemsReportByCity(
  removedItems: RemovedItem[],
  cities: CityData[],
  filename: string = 'removed-items-report.xlsx'
) {
  const workbook = new ExcelJS.Workbook()

  const itemsByCity = removedItems.reduce(
    (acc, item) => {
      if (!acc[item.city_uid]) {
        acc[item.city_uid] = []
      }
      acc[item.city_uid].push(item)
      return acc
    },
    {} as Record<string, RemovedItem[]>
  )

  Object.entries(itemsByCity).forEach(([cityUid, items]) => {
    const city = cities.find(c => c.id === cityUid)
    const cityName = city?.city_name || `City ${cityUid}`

    const worksheet = workbook.addWorksheet(cityName)

    const titleRow = worksheet.addRow([`Món đã xoá tại thành phố ${cityName}`])
    titleRow.font = {
      bold: true,
      size: 14
    }
    titleRow.alignment = { horizontal: 'center' }
    worksheet.mergeCells('A1:E1')

    const headerRow = worksheet.addRow(['Mã món', 'Tên món', 'Giá', 'Người xoá', 'Thời gian xoá'])

    headerRow.font = {
      bold: true,
      color: { argb: 'FFFFFF' }
    }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    }

    items.forEach(item => {
      worksheet.addRow([
        item.item_id,
        item.item_name,
        formatVietnameseCurrency(item.ots_price),
        item.deleted_by,
        formatVietnameseDateTime(item.deleted_at)
      ])
    })

    // Set column widths for columns A through E only
    worksheet.columns = [
      { width: 15 }, // A: Mã món
      { width: 30 }, // B: Tên món
      { width: 15 }, // C: Giá
      { width: 25 }, // D: Người xoá
      { width: 20 } // E: Thời gian xoá
    ]
  })

  const buffer = await workbook.xlsx.writeBuffer()

  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })

  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
