import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Image as ImageIcon } from 'lucide-react'

import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header'

import type { MenuItem } from '../types'

export const createDanhSachMonColumns = (): ColumnDef<MenuItem>[] => {
  const baseColumns: ColumnDef<MenuItem>[] = [
    {
      id: 'index',
      header: () => <div className='text-left'>#</div>,
      cell: ({ row, table }) => {
        const pageIndex = table.getState().pagination.pageIndex
        const pageSize = table.getState().pagination.pageSize
        return <div className='w-[50px] text-left font-medium'>{pageIndex * pageSize + row.index + 1}</div>
      },
      enableSorting: false,
      enableHiding: false,
      size: 60
    }
  ]

  return [
    ...baseColumns,
    {
      accessorKey: 'code',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Mã món' />
      ),
      cell: ({ row }) => (
        <div className='text-left font-medium'>{row.getValue('code')}</div>
      ),
      enableSorting: true
    },
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Tên món' />
      ),
      cell: ({ row }) => (
        <div className='text-left'>{row.getValue('name')}</div>
      ),
      enableSorting: true
    },
    {
      id: 'image',
      header: () => <div>Ảnh</div>,
      cell: ({ row }) => {
        const item = row.original as MenuItem
        return (
          <div className='flex justify-center'>
            <div className='flex items-center justify-center w-12 h-12 bg-gray-100 rounded-md'>
              {item.image ? (
                <img
                  src={item.image}
                  alt={item.name}
                  className='w-12 h-12 object-cover rounded-md'
                />
              ) : (
                <ImageIcon className='w-6 h-6 text-gray-400' />
              )}
            </div>
          </div>
        )
      },
      enableSorting: false
    },
    {
      accessorKey: 'group',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Nhóm món' />
      ),
      cell: ({ row }) => (
        <div className='text-left'>{row.getValue('group')}</div>
      ),
      enableSorting: true
    },
    {
      accessorKey: 'type',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Loại món' />
      ),
      cell: ({ row }) => (
        <div className='text-left'>{row.getValue('type')}</div>
      ),
      enableSorting: true
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Trạng thái' />
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string
        return (
          <div className='flex justify-center'>
            <Badge variant={status === 'available' ? 'default' : 'secondary'}>
              {status === 'available' ? 'Có bán' : 'Hết hàng'}
            </Badge>
          </div>
        )
      },
      enableSorting: true
    },
    {
      accessorKey: 'canTakeaway',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Bán tại chỗ' />
      ),
      cell: ({ row }) => {
        const canTakeaway = row.getValue('canTakeaway') as boolean
        return (
          <div className='flex justify-center'>
            {canTakeaway ? (
              <CheckCircle className='h-5 w-5 text-green-500' />
            ) : (
              <XCircle className='h-5 w-5 text-red-500' />
            )}
          </div>
        )
      },
      enableSorting: true
    },
    {
      accessorKey: 'canDelivery',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Bán mang đi' />
      ),
      cell: ({ row }) => {
        const canDelivery = row.getValue('canDelivery') as boolean
        return (
          <div className='flex justify-center'>
            {canDelivery ? (
              <CheckCircle className='h-5 w-5 text-green-500' />
            ) : (
              <XCircle className='h-5 w-5 text-red-500' />
            )}
          </div>
        )
      },
      enableSorting: true
    },
    {
      accessorKey: 'order',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Thứ tự' />
      ),
      cell: ({ row }) => (
        <div className='text-center'>{row.getValue('order')}</div>
      ),
      enableSorting: true
    },
    {
      accessorKey: 'lastUpdated',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Thời gian cập nhật' />
      ),
      cell: ({ row }) => (
        <div className='text-left text-sm text-muted-foreground'>
          {row.getValue('lastUpdated')}
        </div>
      ),
      enableSorting: true
    }
  ]
}
