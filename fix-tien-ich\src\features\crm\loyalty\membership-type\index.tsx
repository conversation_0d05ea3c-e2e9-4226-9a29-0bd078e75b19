import { PlusCircle } from 'lucide-react'

import { Main } from '@/components/layout/main'
import { <PERSON><PERSON>, Card, CardContent, CardHeader } from '@/components/ui'

import { MembershipTypeTable, MembershipTypeModalForm } from './components'
import { MembershipTypeProvider, useMembershipTypeContext } from './context'

function MembershipTypeContent() {
  const { editDialogOpen, setEditDialogOpen, selectedMembershipType, setSelectedMembershipType } =
    useMembershipTypeContext()

  const handleCreateNew = () => {
    setSelectedMembershipType(null)
    setEditDialogOpen(true)
  }

  return (
    <Main>
      <div className='container mx-auto space-y-6 py-6'>
        <div className='flex items-center justify-between'>
          <h1 className='text-3xl font-bold tracking-tight'>Hạng thành viên</h1>
        </div>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
            <Button variant='default' onClick={handleCreateNew}>
              <PlusCircle className='mr-2 h-4 w-4' />
              Tạo mới
            </Button>
          </CardHeader>
          <CardContent>
            <MembershipTypeTable />
          </CardContent>
        </Card>

        <MembershipTypeModalForm
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          membershipType={selectedMembershipType || undefined}
        />
      </div>
    </Main>
  )
}

export default function MembershipTypePage() {
  return (
    <MembershipTypeProvider>
      <MembershipTypeContent />
    </MembershipTypeProvider>
  )
}
