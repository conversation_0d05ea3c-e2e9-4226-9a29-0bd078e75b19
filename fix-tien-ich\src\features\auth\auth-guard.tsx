import { useEffect } from 'react'
import { useRouter } from '@tanstack/react-router'
import { useCurrentUser } from '@/hooks/use-auth'

interface AuthGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
}

export function AuthGuard({ 
  children, 
  fallback = null, 
  redirectTo = '/sign-in' 
}: AuthGuardProps) {
  const router = useRouter()
  const { isAuthenticated } = useCurrentUser()

  useEffect(() => {
    if (!isAuthenticated) {
      const currentPath = window.location.pathname + window.location.search
      router.navigate({
        to: redirectTo,
        search: {
          redirect: currentPath
        }
      })
    }
  }, [isAuthenticated, router, redirectTo])

  if (!isAuthenticated) {
    return fallback
  }

  return <>{children}</>
}
