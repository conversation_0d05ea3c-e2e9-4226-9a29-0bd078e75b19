import { ColumnDef, flexRender, getCoreRowModel, useReactTable, getPaginationRowModel } from '@tanstack/react-table'

import { DataTablePagination } from '@/components/data-table/data-table-pagination'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui'

import { getActionLabel } from '../constants/action-mappings'
import { SystemLog } from '../data'

interface SystemLogTableProps {
  logs: SystemLog[]
  isLoading: boolean
  formatTimestamp: (timestamp: string) => string
}

export function SystemLogTable({ logs, isLoading, formatTimestamp }: SystemLogTableProps) {
  const columns: ColumnDef<SystemLog>[] = [
    {
      id: 'index',
      header: '#',
      cell: ({ row }) => <div className='w-[50px]'>{row.index + 1}</div>,
      enableSorting: false,
      enableHiding: false,
      size: 50
    },
    {
      accessorKey: 'request_path',
      header: 'Thao tác',
      size: 500, // 50% equivalent
      cell: ({ getValue }) => {
        const value = getValue() as string
        return getActionLabel(value)
      }
    },
    {
      accessorKey: 'user_name',
      header: 'User',
      size: 250 // 25% equivalent
    },
    {
      accessorKey: 'request_at',
      header: 'Thời gian',
      size: 250, // 25% equivalent
      cell: ({ getValue }) => {
        const value = getValue() as string
        return formatTimestamp(value)
      }
    }
  ]

  const table = useReactTable({
    data: logs,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 20
      }
    }
  })

  if (isLoading) {
    return (
      <div className='py-8 text-center'>
        <p>Đang tải dữ liệu nhật ký...</p>
      </div>
    )
  }

  return (
    <div className='space-y-4'>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id} style={{ width: header.getSize() }}>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  Không có dữ liệu nhật ký
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />
    </div>
  )
}
