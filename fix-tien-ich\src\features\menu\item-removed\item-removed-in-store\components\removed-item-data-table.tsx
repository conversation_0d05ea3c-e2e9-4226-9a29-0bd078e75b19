import { useState, useEffect } from 'react'

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  RowSelectionState
} from '@tanstack/react-table'

import { RemovedItem } from '@/types/item-removed'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Button
} from '@/components/ui'

interface RemovedItemDataTableProps {
  columns: ColumnDef<RemovedItem>[]
  data: RemovedItem[]
  onRestoreItem?: (item: RemovedItem) => void
  onBulkRestore?: (selectedItems: RemovedItem[]) => void
  clearSelection?: boolean
}

export function RemovedItemDataTable({
  columns,
  data,
  onRestoreItem,
  onBulkRestore,
  clearSelection
}: RemovedItemDataTableProps) {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})

  // Clear selection when clearSelection prop changes
  useEffect(() => {
    if (clearSelection) {
      setRowSelection({})
    }
  }, [clearSelection])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection
    },
    meta: {
      onRestoreItem
    }
  })

  const selectedRows = table.getFilteredSelectedRowModel().rows
  const selectedItems = selectedRows.map(row => row.original)

  return (
    <div className='space-y-4'>
      {selectedItems.length > 0 && (
        <div className='flex items-center justify-between rounded-md border p-4'>
          <div className='flex items-center gap-2'>
            <span className='text-muted-foreground text-sm'>
              Đã chọn {selectedItems.length} món
            </span>
          </div>
          <Button
            size='sm'
            onClick={() => onBulkRestore?.(selectedItems)}
            className='bg-blue-600 hover:bg-blue-700'
          >
            Khôi phục các món đã chọn
          </Button>
        </div>
      )}

      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  Không có dữ liệu.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
