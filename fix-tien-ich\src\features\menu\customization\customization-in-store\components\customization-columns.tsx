import { ColumnDef } from '@tanstack/react-table'

import { IconCopy, IconTrash } from '@tabler/icons-react'

import { Customization } from '@/types/customizations'

import { Button } from '@/components/ui/button'

interface CustomizationColumnsProps {
  onCopyCustomization: (customization: Customization) => void
  onDeleteCustomization: (customization: Customization) => void
}

export const customizationColumns = ({
  onCopyCustomization,
  onDeleteCustomization
}: CustomizationColumnsProps): ColumnDef<Customization>[] => [
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => {
      return <div className='text-center'>{row.index + 1}</div>
    },
    size: 60
  },
  {
    accessorKey: 'name',
    header: 'Tên customization',
    cell: ({ row }) => {
      const customization = row.original
      return (
        <div className='flex flex-col'>
          <span className='font-medium'>{customization.name}</span>
        </div>
      )
    },
    size: 250
  },
  {
    accessorKey: 'cityName',
    header: 'Thành phố',
    cell: ({ row }) => {
      const customization = row.original
      return <div className='text-sm'>{customization.cityName || 'Không xác định'}</div>
    },
    size: 120
  },
  {
    accessorKey: 'storeName',
    header: 'Cửa hàng',
    cell: ({ row }) => {
      const customization = row.original
      return <div className='text-sm'>{customization.storeName || 'Cửa hàng hiện tại'}</div>
    },
    size: 150
  },
  {
    id: 'copy',
    header: () => <div className='text-center'>Sao chép</div>,
    cell: ({ row }) => {
      const customization = row.original
      return (
        <div className='flex items-center justify-center'>
          <Button
            variant='ghost'
            size='sm'
            onClick={e => {
              e.stopPropagation()
              onCopyCustomization(customization)
            }}
            className='h-8 w-8 p-0'
          >
            <IconCopy className='h-4 w-4' />
          </Button>
        </div>
      )
    },
    size: 100
  },
  {
    id: 'actions',
    header: () => <div className='text-center'></div>,
    cell: ({ row }) => {
      const customization = row.original
      return (
        <div className='flex items-center justify-center'>
          <Button
            variant='ghost'
            size='sm'
            onClick={e => {
              e.stopPropagation()
              onDeleteCustomization(customization)
            }}
            className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
          >
            <IconTrash className='h-4 w-4' />
          </Button>
        </div>
      )
    },
    size: 80
  }
]
