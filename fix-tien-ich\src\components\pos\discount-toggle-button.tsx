import { useState } from 'react'

import { toast } from 'sonner'

import { Button } from '@/components/ui'

interface DiscountStatusButtonProps {
  isActive: boolean
  onToggle: () => Promise<void>
  disabled?: boolean
  className?: string
}

export function DiscountStatusButton({
  isActive,
  onToggle,
  disabled = false,
  className = ''
}: DiscountStatusButtonProps) {
  const [isToggling, setIsToggling] = useState(false)

  const handleToggle = async () => {
    if (disabled || isToggling) return

    setIsToggling(true)
    try {
      await onToggle()
      toast.success(isActive ? 'Đ<PERSON> vô hiệu hóa chương trình giảm giá' : '<PERSON><PERSON> kích hoạt chương trình giảm giá')
    } catch (error) {
      toast.error('Có lỗi xảy ra khi cập nhật trạng thái')
      console.error('Toggle error:', error)
    } finally {
      setIsToggling(false)
    }
  }

  return (
    <Button
      type='button'
      variant={isActive ? 'destructive' : 'default'}
      size='sm'
      onClick={handleToggle}
      disabled={disabled || isToggling}
      className={`min-w-[100px] ${className}`}
    >
      {isToggling && 'Đang cập nhật...'}
      {!isToggling && (isActive ? 'Deactivate' : 'Activate')}
    </Button>
  )
}
