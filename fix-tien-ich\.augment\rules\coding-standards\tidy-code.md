---
type: "always_apply"
---

# Tidy Code Style Standards

## 1. Naming & Readability
- **Choose names that reveal intent**: Use descriptive variable and function names
- **Consistent conventions**: camelCase for variables/functions,kebab-case for components
- **Function names indicate actions**: `handleClick`, `fetchData`, `onSubmit`
- **Meaningful naming**: Avoid unclear abbreviations (`userAccount` not `usrAcc`), use descriptive constant names (`MAX_RETRY_ATTEMPTS` not `3`)
- **Single-line if → no curly braces**: If an if statement has only one line of code, the curly braces { } are removed. This also applies to single-line callbacks (e.g., inside .map(), .filter()).
## 2. Component Structure
- **Single responsibility**: Keep each component focused on one purpose
- **Split large components**: Extract smaller, focused components
- **Logical grouping**: Group related JSX elements together

## 3. Prop Management
- **Clean prop passing**: Use object destructuring, avoid unnecessary data
- **Avoid props drilling**: Use Context or state management for deep data

## 4. Logic & Conditions
- **Use early returns** to reduce nesting
- **Avoid deeply nested conditions** or complex `if` statements
- **Extract complex conditions** into clearly named variables
- **Use positive variable names**: Avoid multiple negations (`!`)

## 5. JSX Conditional Rendering
- **Use && operator**: `condition && <Component />` for simple conditions
- **Use ternary for alternatives**: `condition ? <A /> : <B />` when rendering different content
- **Follow logic rules**: Apply condition extraction and readability principles from Logic & Conditions

## 6. State & Data Handling
- **Keep only UI-relevant data** in state
- **Derive values when possible**: Don't store computed values in state
- **Group related state** into objects when logical

## 7. Side Effects
- **Use useEffect sparingly**: Only when necessary for side effects
- **Accurate dependencies**: Keep dependency arrays minimal and correct
- **Clean up resources**: Always cleanup subscriptions and timers

## 8. Event Handling
- **Extract handlers outside JSX**: Avoid inline functions for reused handlers
- **Consistent naming**: `handle<Action>` or `on<Action>`

## 9. Imports & File Organization
- **Group imports by source**: external libraries → internal aliases → local files
- **Remove unused imports**: Keep imports clean
- **Combine same-library imports**: `import { A, B, C } from 'library'`

## 10. Constants & Configuration
- **Extract magic numbers and strings** into named constants
- **Centralize shared constants** in config files

## 11. Styling
- **Break down long class strings** into variables or components
- **Name classes by purpose**: `error-message` not `red-text`
- **Separate styling from logic**: Keep business logic and styling concerns apart

## 12. Documentation
- **Code should be self-documenting**: Prefer clear naming over comments, mustn't comment code
- **Remove outdated comments**: Keep documentation current
- **Exception**: Complex algorithms may need brief explanations
- **NO README**: No need creating readme whenever we finish
