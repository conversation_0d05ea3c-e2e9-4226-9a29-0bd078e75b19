import { useCallback } from 'react'

import { UseFormReturn } from 'react-hook-form'

import { useNavigate } from '@tanstack/react-router'

import { Button, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input } from '@/components/ui'

import { CreateUserFormData } from '../data'
import { useCreateAccountForm, useAccountManagement, usePermissionManagement } from '../hooks'
import { ACCOUNT_ROUTES } from '../utils/navigation'
import { PermissionSection } from './permission-section'

const FormFieldWrapper = ({
  name,
  label,
  placeholder,
  type = 'text',
  required = false,
  form
}: {
  name: keyof CreateUserFormData
  label: string
  placeholder: string
  type?: string
  required?: boolean
  form: UseFormReturn<CreateUserFormData>
}) => (
  <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <FormItem>
        <FormLabel>
          {label} {required && <span className='text-red-500'>*</span>}
        </FormLabel>
        <FormControl>
          <Input placeholder={placeholder} type={type} {...field} />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
)

export default function CreateAccountPage() {
  const navigate = useNavigate()
  const { form, resetForm } = useCreateAccountForm()
  const { createUser, permissionCategories, isLoading } = useAccountManagement()
  const { togglePermission, isPermissionSelected } = usePermissionManagement(form)

  const handleSubmit = useCallback(
    async (data: CreateUserFormData) => {
      try {
        await createUser(data)
        resetForm()
        navigate({ to: ACCOUNT_ROUTES.LIST })
      } catch (error) {
        console.error('Error creating account:', error)
      }
    },
    [createUser, resetForm, navigate]
  )

  const handlePermissionChange = useCallback(
    (permissionId: string, isChecked: boolean) => {
      togglePermission(permissionId, isChecked)
    },
    [togglePermission]
  )

  return (
    <div className='mx-auto px-4 py-8'>
      <div className='max-w-4xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            <FormFieldWrapper
              name='username'
              label='Tên người dùng'
              placeholder='Nhập tên người dùng'
              required
              form={form}
            />

            <FormFieldWrapper name='email' label='Email' placeholder='Nhập email' type='email' form={form} />

            <FormFieldWrapper
              name='password'
              label='Mật khẩu'
              placeholder='Nhập mật khẩu'
              type='password'
              required
              form={form}
            />

            <PermissionSection
              categories={permissionCategories}
              isPermissionSelected={isPermissionSelected}
              onPermissionChange={handlePermissionChange}
              hasError={!!form.formState.errors.permissions}
              errorMessage={form.formState.errors.permissions?.message}
            />

            <Button type='submit' disabled={isLoading} className='mt-3'>
              {isLoading ? 'Đang tạo...' : 'Xác nhận'}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  )
}
