import { Check, ChevronsUpDown } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

import { Button, Input, Label } from '@/components/ui'

import { DEVICE_TYPE_OPTIONS } from '../../constants/device-form-constants'
import { DeviceFormData } from '../../types'

interface DeviceBasicInfoProps {
  formData: DeviceFormData
  setFormData: (data: DeviceFormData | ((prev: DeviceFormData) => DeviceFormData)) => void
  isEditMode: boolean
  openDeviceType: boolean
  setOpenDeviceType: (open: boolean) => void
}

export function DeviceBasicInfo({
  formData,
  setFormData,
  isEditMode,
  openDeviceType,
  setOpenDeviceType
}: DeviceBasicInfoProps) {
  return (
    <div className='space-y-6'>
      <h3 className='text-lg font-semibold text-gray-900'>Chi tiết</h3>
      <div className='space-y-4'>
        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium text-gray-700'>Tên thiết bị</Label>
          <div className='col-span-2'>
            <Input
              value={formData.deviceName}
              onChange={e => setFormData(prev => ({ ...prev, deviceName: e.target.value }))}
              placeholder='Nhập tên thiết bị'
            />
          </div>
        </div>

        {isEditMode && (
          <div className='grid grid-cols-3 items-center gap-4'>
            <Label className='text-right font-medium text-gray-700'>Device code</Label>
            <div className='col-span-2'>
              <Input value={formData.deviceCode} className='cursor-not-allowed bg-gray-50 font-medium text-gray-600' />
            </div>
          </div>
        )}

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium text-gray-700'>Loại thiết bị</Label>
          <div className='col-span-2'>
            {isEditMode ? (
              <Input value={formData.deviceType} className='cursor-not-allowed bg-gray-50 font-medium text-blue-500' />
            ) : (
              <Popover open={openDeviceType} onOpenChange={setOpenDeviceType}>
                <PopoverTrigger asChild>
                  <Button
                    variant='outline'
                    role='combobox'
                    aria-expanded={openDeviceType}
                    className='w-full justify-between'
                  >
                    {formData.deviceType || 'Chọn loại thiết bị'}
                    <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className='w-full p-0'>
                  <Command>
                    <CommandInput placeholder='Tìm kiếm loại thiết bị...' />
                    <CommandList>
                      <CommandEmpty>Không tìm thấy loại thiết bị.</CommandEmpty>
                      <CommandGroup>
                        {DEVICE_TYPE_OPTIONS.map(option => (
                          <CommandItem
                            key={option.value}
                            value={option.value}
                            onSelect={() => {
                              setFormData(prev => ({ ...prev, deviceType: option.value }))
                              setOpenDeviceType(false)
                            }}
                          >
                            <Check
                              className={cn(
                                'mr-2 h-4 w-4',
                                formData.deviceType === option.value ? 'opacity-100' : 'opacity-0'
                              )}
                            />
                            {option.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>

        <div className='grid grid-cols-3 items-center gap-4'>
          <Label className='text-right font-medium text-gray-700'>Điểm bán hàng</Label>
          <div className='col-span-2'>
            <Input
              value={formData.storeLocation}
              disabled
              className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
            />
          </div>
        </div>

        {isEditMode && (
          <div className='grid grid-cols-3 items-center gap-4'>
            <Label className='text-right font-medium text-gray-700'>Địa chỉ IP local</Label>
            <div className='col-span-2'>
              <Input
                value={formData.localIpAddress}
                disabled
                className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
              />
            </div>
          </div>
        )}

        {isEditMode && (
          <div className='grid grid-cols-3 items-center gap-4'>
            <Label className='text-right font-medium text-gray-700'>Phiên bản</Label>
            <div className='col-span-2'>
              <Input
                value={formData.version}
                disabled
                className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
              />
            </div>
          </div>
        )}

        {isEditMode && (
          <div className='grid grid-cols-3 items-center gap-4'>
            <Label className='text-right font-medium text-gray-700'>Múi giờ tại POS</Label>
            <div className='col-span-2'>
              <Input
                value={formData.timezone}
                disabled
                className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
              />
            </div>
          </div>
        )}

        {isEditMode && (
          <>
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Thời gian cập nhật</Label>
              <div className='col-span-2'>
                <Input
                  value={formData.lastUpdate}
                  className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
                />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
