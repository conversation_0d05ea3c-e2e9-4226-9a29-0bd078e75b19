import { createContext, useContext, useState, ReactNode, useMemo } from 'react'

import {
  ReportsSourcesData,
  ReportsStoresData,
  ReportsWeekdaysData,
  SaleSummaryOverviewData,
  KtvMonthReportData,
  PromotionSaleSummaryData,
  ItemSaleSummaryData
} from '@/types/api'

import { usePosStores, useCurrentCompany } from '@/stores/posStore'

import {
  useSaleSummaryOverview,
  useReportsSources,
  useReportsStores,
  useReportsWeekdays,
  usePromotionSaleSummary,
  useItemSaleSummary,
  useKtvMonthReport
} from '@/hooks/api'

import type { SortOption } from '../components/sort-dropdown'

interface DashboardContextValue {
  dateRange: { from: Date; to: Date }
  setDateRange: (range: { from: Date; to: Date }) => void

  selectedStores: string[]
  setSelectedStores: (stores: string[]) => void

  itemsSortBy: SortOption
  setItemsSortBy: (sortBy: SortOption) => void

  ktvTimestamp: number
  setKtvTimestamp: (timestamp: number) => void

  defaultDateRange: { from: Date; to: Date }

  overviewData: SaleSummaryOverviewData | null
  isOverviewLoading: boolean
  overviewError: string | null

  sourcesData: ReportsSourcesData[]
  isSourcesLoading: boolean
  sourcesError: string | null

  storesData: ReportsStoresData[]
  isStoresLoading: boolean
  storesError: string | null

  weekdaysData: ReportsWeekdaysData[]
  isWeekdaysLoading: boolean
  weekdaysError: string | null

  promotionsData: PromotionSaleSummaryData[]
  isPromotionsLoading: boolean
  promotionsError: string | null

  itemsData: ItemSaleSummaryData[]
  isItemsLoading: boolean
  itemsError: string | null

  ktvReportData: KtvMonthReportData | null
  isKtvReportLoading: boolean
  ktvReportError: string | null
}

const DashboardContext = createContext<DashboardContextValue | undefined>(undefined)

interface DashboardProviderProps {
  children: ReactNode
}

export function DashboardProvider({ children }: DashboardProviderProps) {
  const [dateRange, setDateRange] = useState(() => {
    const now = new Date()

    return {
      from: now,
      to: now
    }
  })

  const [selectedStores, setSelectedStores] = useState<string[]>(['all-stores'])
  const [itemsSortBy, setItemsSortBy] = useState<SortOption>('revenue_net')

  // Default to August 2025 (1753981200000)
  const [ktvTimestamp, setKtvTimestamp] = useState<number>(1753981200000)

  const { currentBrandStores, selectedBrand } = usePosStores()
  const { company } = useCurrentCompany()

  const listStoreUid = useMemo(() => {
    if (!currentBrandStores || currentBrandStores.length === 0) {
      return ''
    }

    const actualSelectedStores = selectedStores.filter(store => store !== 'all-stores')

    if (actualSelectedStores.length === 0) {
      return currentBrandStores.map(store => store.id).join(',')
    }

    return actualSelectedStores.join(',')
  }, [selectedStores, currentBrandStores])

  const startOfDay = useMemo(() => {
    const date = new Date(dateRange.from)
    date.setHours(0, 0, 0, 0)
    return date.getTime()
  }, [dateRange.from])

  const endOfDay = useMemo(() => {
    const date = new Date(dateRange.to)
    date.setHours(23, 59, 59, 999)
    return date.getTime()
  }, [dateRange.to])

  const defaultDateRange = useMemo(() => {
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    yesterday.setHours(23, 59, 59, 999)

    const sevenDaysAgo = new Date(yesterday.getTime() - 6 * 24 * 60 * 60 * 1000)
    sevenDaysAgo.setHours(0, 0, 0, 0)
    return {
      from: sevenDaysAgo,
      to: yesterday
    }
  }, [])

  const {
    data: overviewResponse,
    isLoading: isOverviewLoading,
    error: overviewApiError
  } = useSaleSummaryOverview({
    params: {
      list_store_uid: listStoreUid,
      start_date: startOfDay,
      end_date: endOfDay,
      store_open_at: 0
    },
    enabled: !!listStoreUid
  })

  const {
    data: sourcesResponse,
    isLoading: isSourcesLoading,
    error: sourcesApiError
  } = useReportsSources({
    params: {
      brand_uid: selectedBrand?.id || '',
      company_uid: company?.id || '',
      list_store_uid: listStoreUid,
      start_date: defaultDateRange.from.getTime(),
      end_date: defaultDateRange.to.getTime(),
      store_open_at: 0,
      limit: 5
    },
    enabled: !!listStoreUid && !!selectedBrand?.id && !!company?.id
  })

  const sourcesData = sourcesResponse?.data || []
  const sourcesError = sourcesApiError?.message || null

  const {
    data: weekdaysResponse,
    isLoading: isWeekdaysLoading,
    error: weekdaysApiError
  } = useReportsWeekdays({
    params: {
      brand_uid: selectedBrand?.id || '',
      company_uid: company?.id || '',
      list_store_uid: listStoreUid,
      start_date: defaultDateRange.from.getTime(),
      end_date: defaultDateRange.to.getTime(),
      store_open_at: 0,
      limit: 7
    },
    enabled: !!listStoreUid && !!selectedBrand?.id && !!company?.id
  })

  const weekdaysData = weekdaysResponse?.data || []
  const weekdaysError = weekdaysApiError?.message || null

  const {
    data: storesResponse,
    isLoading: isStoresLoading,
    error: storesApiError
  } = useReportsStores({
    params: {
      brand_uid: selectedBrand?.id || '',
      company_uid: company?.id || '',
      list_store_uid: listStoreUid,
      start_date: defaultDateRange.from.getTime(),
      end_date: defaultDateRange.to.getTime(),
      store_open_at: 0,
      limit: 5
    },
    enabled: !!listStoreUid && !!selectedBrand?.id && !!company?.id
  })

  const storesData = storesResponse?.data || []
  const storesError = storesApiError?.message || null

  const {
    data: promotionsData,
    isLoading: isPromotionsLoading,
    error: promotionsApiError
  } = usePromotionSaleSummary({
    params: {
      brand_uid: selectedBrand?.id || '',
      company_uid: company?.id || '',
      list_store_uid: listStoreUid,
      start_date: defaultDateRange.from.getTime(),
      end_date: defaultDateRange.to.getTime(),
      store_open_at: 0,
      limit: 5
    },
    enabled: !!listStoreUid && !!selectedBrand?.id && !!company?.id
  })

  const promotionsError = promotionsApiError?.message || null

  const {
    data: itemsData,
    isLoading: isItemsLoading,
    error: itemsApiError
  } = useItemSaleSummary({
    params: {
      brand_uid: selectedBrand?.id || '',
      company_uid: company?.id || '',
      list_store_uid: listStoreUid,
      start_date: defaultDateRange.from.getTime(),
      end_date: defaultDateRange.to.getTime(),
      store_open_at: 0,
      limit: 10,
      order_by: itemsSortBy
    },
    enabled: !!listStoreUid && !!selectedBrand?.id && !!company?.id
  })

  const itemsError = itemsApiError?.message || null

  const overviewData = overviewResponse?.data || null
  const overviewError = overviewApiError?.message || null

  const firstStoreUid = useMemo(() => {
    if (!listStoreUid) return ''
    return listStoreUid.split(',')[0]
  }, [listStoreUid])

  const {
    data: ktvReportResponse,
    isLoading: isKtvReportLoading,
    error: ktvReportApiError
  } = useKtvMonthReport({
    params: {
      store_uid: firstStoreUid,
      date_time: ktvTimestamp
    },
    enabled: !!firstStoreUid
  })

  const ktvReportData = ktvReportResponse || null

  const ktvReportError = ktvReportApiError?.message || null

  const contextValue: DashboardContextValue = {
    dateRange,
    setDateRange,
    selectedStores,
    setSelectedStores,
    itemsSortBy,
    setItemsSortBy,
    ktvTimestamp,
    setKtvTimestamp,
    defaultDateRange,
    overviewData,
    isOverviewLoading,
    overviewError,
    sourcesData,
    isSourcesLoading,
    sourcesError,
    storesData,
    isStoresLoading,
    storesError,
    weekdaysData,
    isWeekdaysLoading,
    weekdaysError,
    promotionsData,
    isPromotionsLoading,
    promotionsError,
    itemsData,
    isItemsLoading,
    itemsError,
    ktvReportData,
    isKtvReportLoading,
    ktvReportError
  }

  return <DashboardContext.Provider value={contextValue}>{children}</DashboardContext.Provider>
}

export function useDashboardContext() {
  const context = useContext(DashboardContext)
  if (context === undefined) {
    throw new Error('useDashboardContext must be used within a DashboardProvider')
  }
  return context
}
