import { useState } from 'react'

import { toast } from 'sonner'

import { useCurrentBrand } from '@/stores/posStore'

import { crmSettingsApi } from '@/lib/api/crm/settings-api'

import { usePosCompanyData } from '@/hooks/local-storage'

import type { CrmSettingsFormValues } from '../data'

export function useSettingsApi() {
  const [isLoading, setIsLoading] = useState(false)
  const { selectedBrand } = useCurrentBrand()
  const saveBrandConfig = async (data: Partial<CrmSettingsFormValues>) => {
    setIsLoading(true)
    try {
      const updateData = {
        id: selectedBrand?.brandId || '',
        Hotline: data.hotline,
        image: data.bannerImage,
        Manager_Email_List: data.email?.join(', ') || ''
      }

      await crmSettingsApi.updateBrandConfig(selectedBrand?.brandId || '', updateData)

      toast.success('<PERSON><PERSON>u hình thương hiệu đã được lưu thành công')
    } catch (error) {
      console.error('Error saving brand config:', error)
      toast.error('C<PERSON> lỗi xảy ra khi lưu cấu hình thương hiệu')
    } finally {
      setIsLoading(false)
    }
  }

  const saveDeliveryConfig = async (data: Partial<CrmSettingsFormValues>) => {
    setIsLoading(true)
    const { selectedBrand } = useCurrentBrand()
    try {
      const updateData = {
        id: '6886d8581ed12b0001d6672e',
        pos_parent: selectedBrand?.brandId || '',
        vat: data.vat,
        service_charge: data.serviceCharge,
        warning_cheat_mail_config: {
          mailType: 'Brand'
        },
        alert_ws_mail_config: {
          mailType: 'Brand'
        }
      }

      await crmSettingsApi.updateSettingConfig(selectedBrand?.brandId || '', updateData)

      toast.success('Cấu hình đặt giao hàng đã được lưu thành công')
    } catch (error) {
      console.error('Error saving delivery config:', error)
      toast.error('Có lỗi xảy ra khi lưu cấu hình đặt giao hàng')
    } finally {
      setIsLoading(false)
    }
  }

  const saveMembershipConfig = async (data: Partial<CrmSettingsFormValues>) => {
    setIsLoading(true)
    const company = usePosCompanyData()
    try {
      const updateData = {
        company_id: company?.id || '',
        is_number_rounding: data.enablePointAccumulation ? 1 : 0,
        reset_point_cycle: data.resetPointsDays,
        membership_downgrade_cycle: data.autoUpgradeDays,
        none_point_sources: data.excludeInvoiceOrigins?.join(',') || '',
        id: '6886d8505ae6c800017f6fb2',
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }

      await crmSettingsApi.updateLoyaltySettings(selectedBrand?.brandId || '', updateData)

      toast.success('Chương trình thành viên đã được lưu thành công')
    } catch (error) {
      console.error('Error saving membership config:', error)
      toast.error('Có lỗi xảy ra khi lưu chương trình thành viên')
    } finally {
      setIsLoading(false)
    }
  }

  const saveTransactionAlert = async (data: Partial<CrmSettingsFormValues>) => {
    setIsLoading(true)
    try {
      const updateData = {
        limit_eat_count_per_day: data.balanceThreshold,
        limit_pay_amount_per_day: data.balanceThresholdVND
      }

      await crmSettingsApi.updateCheatConfig(selectedBrand?.brandId || '', updateData)

      toast.success('Cảnh báo giao dịch bất thường đã được lưu thành công')
    } catch (error) {
      console.error('Error saving transaction alert:', error)
      toast.error('Có lỗi xảy ra khi lưu cảnh báo giao dịch bất thường')
    } finally {
      setIsLoading(false)
    }
  }

  const saveOnlineOrderAlert = async (data: Partial<CrmSettingsFormValues>) => {
    setIsLoading(true)
    try {
      const updateData = {
        alert_ws_mail_config: {
          mailType: data.onlineOrderEmailType === 'other' ? 'Custom' : 'Brand',
          mailList: data.onlineOrderEmailType === 'other' ? data.onlineOrderCustomEmails : undefined
        },
        id: '6886d8581ed12b0001d6672e'
      }

      await crmSettingsApi.updateSettingConfig(selectedBrand?.brandId || '', updateData)

      toast.success('Cảnh báo đơn hàng online đã được lưu thành công')
    } catch (error) {
      console.error('Error saving online order alert:', error)
      toast.error('Có lỗi xảy ra khi lưu cảnh báo đơn hàng online')
    } finally {
      setIsLoading(false)
    }
  }

  return {
    isLoading,
    saveBrandConfig,
    saveDeliveryConfig,
    saveMembershipConfig,
    saveTransactionAlert,
    saveOnlineOrderAlert
  }
}
