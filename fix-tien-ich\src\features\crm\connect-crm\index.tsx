import { useNavigate } from '@tanstack/react-router'

import { IconLink, IconLoader2 } from '@tabler/icons-react'

import { useCurrentCompany } from '@/stores/posStore'

import { authenticateCrmByToken } from '@/lib/api/crm'

import { useConnectCrm } from '@/hooks/crm/use-connect-crm'
import { useCurrentUser } from '@/hooks/use-auth'
import { usePosData } from '@/hooks/use-pos-data'

import { Main } from '@/components/layout/main'
import { Button, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui'

export default function ConnectCrmPage() {
  const navigate = useNavigate()
  const connectCrmMutation = useConnectCrm()
  const { brands } = usePosData()
  const { user } = useCurrentUser()
  const { company } = useCurrentCompany()

  const extractTokenFromUrl = (url: string): string | null => {
    try {
      const urlObj = new URL(url)
      const token = urlObj.searchParams.get('token')
      return token
    } catch (error) {
      return null
    }
  }

  const handleConnectCrm = async () => {
    if (!user?.email || !company?.id || !user.company_uid) {
      return
    }

    try {
      const payload = {
        brand_uid: brands[0].id,
        company_uid: company.id,
        email: user.email
      }

      const response = await connectCrmMutation.mutateAsync(payload)

      if (response.data?.link) {
        const token = extractTokenFromUrl(response.data.link)
        if (token) {
          const authResponse = await authenticateCrmByToken(token, () => {
            // Callback khi authentication thành công
            // Navigate đến customer-list page
            navigate({ to: '/crm/customer-list' })
          })

          if (authResponse.status === 'OK' && authResponse.token) {
            localStorage.setItem('crm_token', authResponse.token)
            // Navigate ngay lập tức nếu callback không được gọi
            navigate({ to: '/crm/customer-list' })
          }
        }
      }
    } catch (error) {
      console.error('Error connecting to CRM:', error)
    }
  }

  return (
    <Main>
      <div className='container mx-auto flex min-h-screen items-center justify-center py-6'>
        <div className='space-y-4 text-center'>
          <div>
            <h1 className='text-2xl font-bold tracking-tight'>Kết nối CRM</h1>
            <p className='text-muted-foreground'>Thiết lập kết nối với hệ thống CRM để đồng bộ dữ liệu</p>
          </div>

          <Card className='max-w-md'>
            <CardHeader className='pb-3'>
              <CardTitle className='flex items-center justify-center gap-2 text-lg font-semibold'>
                <IconLink className='size-5 text-blue-600' />
                Kết nối hệ thống
              </CardTitle>
              <CardDescription>Nhấn nút bên dưới để bắt đầu quá trình kết nối CRM</CardDescription>
            </CardHeader>
            <CardContent className='pt-0'>
              <Button onClick={handleConnectCrm} disabled={connectCrmMutation.isPending} className='w-full' size='lg'>
                {connectCrmMutation.isPending && (
                  <>
                    <IconLoader2 className='mr-2 size-4 animate-spin' />
                    Đang kết nối...
                  </>
                )}
                {!connectCrmMutation.isPending && 'Kết nối CRM'}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </Main>
  )
}
