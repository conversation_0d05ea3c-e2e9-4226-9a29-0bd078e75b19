import { ReportsWeekdaysData } from '@/types/api'
import { <PERSON>, <PERSON><PERSON>hart, ResponsiveContainer, Tooltip, XAxis, <PERSON>A<PERSON>s } from 'recharts'

// Transform API data to chart format
const transformWeekdaysData = (weekdaysData: ReportsWeekdaysData[]) => {
  const weekdayMapping = {
    MONDAY: 'T2',
    TUESDAY: 'T3',
    WEDNESDAY: 'T4',
    THURSDAY: 'T5',
    FRIDAY: 'T6',
    SATURDAY: 'T7',
    SUNDAY: 'CN'
  }

  const transformedData = weekdaysData.map(item => ({
    name: weekdayMapping[item.day_of_week],
    total: item.revenue,
    transactions: item.transaction_quantity
  }))

  // Sort by weekday order: T2, T3, T4, T5, T6, T7, CN
  const weekdayOrder = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN']
  return transformedData.sort((a, b) => {
    const indexA = weekdayOrder.indexOf(a.name)
    const indexB = weekdayOrder.indexOf(b.name)
    return indexA - indexB
  })
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const value = payload[0].value
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('vi-VN').format(amount)
    }

    return (
      <div className='rounded-lg border bg-white p-3 shadow-lg'>
        <p className='font-semibold text-gray-900'>{label}</p>
        <p className='text-xs text-gray-600'>Doanh thu ngày trong tuần: {formatCurrency(value)} đ</p>
      </div>
    )
  }

  return null
}

interface OverviewProps {
  weekdaysData?: ReportsWeekdaysData[]
}

export function Overview({ weekdaysData }: OverviewProps) {
  if (!weekdaysData || weekdaysData.length === 0) {
    return (
      <div className='flex h-[350px] items-center justify-center'>
        <div className='text-muted-foreground text-sm'>Chưa có thông tin</div>
      </div>
    )
  }

  const chartData = transformWeekdaysData(weekdaysData)

  return (
    <ResponsiveContainer width='100%' height={350}>
      <BarChart data={chartData}>
        <XAxis dataKey='name' stroke='#888888' fontSize={12} tickLine={false} axisLine={false} />
        <YAxis
          stroke='#888888'
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={value => `${(value / 1000000).toFixed(0)}M`}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey='total' fill='currentColor' radius={[4, 4, 0, 0]} className='fill-primary' />
      </BarChart>
    </ResponsiveContainer>
  )
}
