import { ColumnDef } from '@tanstack/react-table'
import { Store } from 'lucide-react'

import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header'

import type { Store as StoreType } from '../types'
import { StoreSettingsDropdown } from './store-settings-dropdown'

interface StoreColumnsCallbacks {
  onStoreInfoClick?: (store: StoreType) => void
  onMenuClick?: (store: StoreType) => void
  onDeliveryTimeClick?: (store: StoreType) => void
  onShippingFeeClick?: (store: StoreType) => void
}

export const createStoresColumns = (callbacks?: StoreColumnsCallbacks): ColumnDef<StoreType>[] => [
  {
    id: 'index',
    header: () => <div className='text-left'>#</div>,
    cell: ({ row, table }) => {
      const pageIndex = table.getState().pagination.pageIndex
      const pageSize = table.getState().pagination.pageSize
      return <div className='w-[50px] text-left font-medium'>{pageIndex * pageSize + row.index + 1}</div>
    },
    enableSorting: false,
    enableHiding: false,
    size: 60
  },
  {
    accessorKey: 'id',
    header: ({ column }) => (
      <div className='text-center'>
        <DataTableColumnHeader column={column} title='ID' />
      </div>
    ),
    cell: ({ row }) => (
      <div className='text-left'>
        <span className='font-medium'>{row.getValue('id')}</span>
      </div>
    ),
    enableSorting: true,
    size: 100
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <div className='text-center'>
        <DataTableColumnHeader column={column} title='Nhà hàng' />
      </div>
    ),
    cell: ({ row }) => {
      const store = row.original
      return (
        <div className='text-left space-y-2'>
          <div>
            <span className='font-medium'>{row.getValue('name')}</span>
          </div>
          <div>
            <StoreSettingsDropdown
              store={store}
              onStoreInfoClick={callbacks?.onStoreInfoClick}
              onMenuClick={callbacks?.onMenuClick}
              onDeliveryTimeClick={callbacks?.onDeliveryTimeClick || ((store) => {
                console.log('Thời gian bán giao hàng:', store)
              })}
              onShippingFeeClick={callbacks?.onShippingFeeClick || ((store) => {
                console.log('Phí vận chuyển:', store)
              })}
            />
          </div>
        </div>
      )
    },
    enableSorting: true,
    size: 200
  },
  {
    id: 'image',
    header: () => <div className='text-center'>Ảnh</div>,
    cell: ({ row }) => {
      const store = row.original
      return (
        <div className='flex justify-center'>
          <div className='flex items-center justify-center w-12 h-12 bg-gray-100 rounded-md'>
            {store.image ? (
              <img
                src={store.image}
                alt={store.name}
                className='w-12 h-12 object-cover rounded-md'
              />
            ) : (
              <Store className='w-6 h-6 text-gray-400' />
            )}
          </div>
        </div>
      )
    },
    enableSorting: false,
    size: 80
  },
  {
    accessorKey: 'phone',
    header: ({ column }) => (
      <div className='text-center'>
        <DataTableColumnHeader column={column} title='Số điện thoại' />
      </div>
    ),
    cell: ({ row }) => (
      <div className='text-left'>
        <span className='text-sm'>{row.getValue('phone')}</span>
      </div>
    ),
    enableSorting: true,
    size: 120
  },
  {
    accessorKey: 'address',
    header: ({ column }) => (
      <div className='text-center'>
        <DataTableColumnHeader column={column} title='Địa chỉ' />
      </div>
    ),
    cell: ({ row }) => (
      <div className='text-left'>
        <span
          className='text-sm max-w-[300px] truncate block'
          title={row.getValue('address')}
        >
          {row.getValue('address')}
        </span>
      </div>
    ),
    enableSorting: true,
    size: 300
  },
  {
    accessorKey: 'lastSalesDataUpdate',
    header: ({ column }) => (
      <div className='text-center'>
        <DataTableColumnHeader column={column} title='Cập nhật dữ liệu bán hàng lần cuối' />
      </div>
    ),
    cell: ({ row }) => (
      <div className='text-left'>
        <span className='text-sm text-muted-foreground'>
          {row.getValue('lastSalesDataUpdate') || '-'}
        </span>
      </div>
    ),
    enableSorting: true,
    size: 200
  }
]
