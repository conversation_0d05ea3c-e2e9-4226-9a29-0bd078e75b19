import { Button } from '@/components/ui/button'
import { RotateCcw, Eye } from 'lucide-react'

interface ComboSpecialActionBarProps {
  onSyncItems: () => void
  onViewPreview: () => void
  className?: string
}

export function ComboSpecialActionBar({ 
  onSyncItems, 
  onViewPreview,
  className = '' 
}: ComboSpecialActionBarProps) {
  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      <Button 
        variant='outline' 
        onClick={onSyncItems} 
        className='text-orange-600 border-orange-600 hover:bg-orange-50'
      >
        <RotateCcw className='h-4 w-4 mr-2' />
        Đồng bộ combo tùy chỉnh
      </Button>
      <Button variant='outline' onClick={onViewPreview}>
        <Eye className='h-4 w-4 mr-2' />
        Xem trước
      </Button>
    </div>
  )
}
