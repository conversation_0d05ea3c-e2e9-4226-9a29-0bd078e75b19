import { useState } from 'react'

import { ChevronDown } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

export type SortOption = 'revenue_net' | 'quantity_sold' | 'revenue_gross'

interface SortDropdownProps {
  value: SortOption
  onChange: (value: SortOption) => void
}

const sortOptions = [
  { value: 'revenue_net' as const, label: 'Doanh thu (net)' },
  { value: 'quantity_sold' as const, label: 'Số lượng' },
  { value: 'revenue_gross' as const, label: 'Doanh thu (gross)' }
]

export function SortDropdown({ value, onChange }: SortDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)

  const currentOption = sortOptions.find(option => option.value === value)

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' size='sm' className='text-muted-foreground hover:text-foreground h-auto p-0 text-xs'>
          {currentOption?.label}
          <ChevronDown className='ml-1 h-3 w-3' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start' className='w-40'>
        {sortOptions.map(option => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => {
              onChange(option.value)
              setIsOpen(false)
            }}
            className='text-xs'
          >
            {option.label}
            {value === option.value && <span className='ml-auto text-blue-600'>✓</span>}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
