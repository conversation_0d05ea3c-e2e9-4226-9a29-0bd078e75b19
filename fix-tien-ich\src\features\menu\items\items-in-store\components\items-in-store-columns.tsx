'use client'

import type { ColumnDef } from '@tanstack/react-table'

import { Icon<PERSON><PERSON>, IconTrash } from '@tabler/icons-react'

import { Settings } from 'lucide-react'

import { DataTableColumnHeader } from '@/components/data-table'
import { StatusBadge } from '@/components/pos'
import { Badge, Button, Checkbox, Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui'

import { ItemsInStore } from '../data'
import { CustomColumnHeader } from '../exports'

interface ColumnsProps {
  onBuffetConfigClick: (item: any) => void
  onCustomizationClick?: (item: any) => void
}

// Helper functions to handle both ItemsInStore and ItemsInStoreTable types
const getItemName = (item: any): string => {
  return item.name || item.item_name || ''
}

const getItemActive = (item: any): boolean => {
  return item.isActive !== undefined ? item.isActive : item.active === 1
}

const columnsDefinition = ({ onBuffetConfigClick, onCustomizationClick }: ColumnsProps): ColumnDef<any, any>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label='Select row'
        onClick={e => e.stopPropagation()}
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => <div className='w-[50px]'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    accessorKey: 'code',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Mã món' />,
    cell: ({ row }) => <div className='text-sm font-medium'>{row.getValue('code')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'name',
    header: ({ column }) => <CustomColumnHeader column={column} title='Tên món' />,
    cell: ({ row }) => <div className='max-w-[200px] truncate text-sm font-medium'>{row.getValue('name')}</div>,
    enableSorting: true,
    enableHiding: true,
    enableColumnFilter: true,
    filterFn: (row, id, value) => {
      const itemName = row.getValue(id) as string
      return itemName?.toLowerCase().includes(value.toLowerCase())
    }
  },
  {
    accessorKey: 'price',
    header: ({ column }) => <CustomColumnHeader column={column} title='Giá' />,
    cell: ({ row }) => {
      const price = row.getValue('price') as number
      return (
        <div className='text-sm font-medium'>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(price)}
        </div>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'vatPercent',
    header: ({ column }) => <DataTableColumnHeader column={column} title='VAT (%)' />,
    cell: ({ row }) => {
      const vatPercent = row.getValue('vatPercent') as number
      return <div className='text-right text-sm'>{(vatPercent * 100).toFixed(0)}%</div>
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'itemType',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Nhóm món' />,
    cell: ({ row }) => (
      <Badge variant='outline' className='text-xs'>
        {row.getValue('itemType')}
      </Badge>
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'itemClass',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Loại món' />,
    cell: ({ row }) =>
      row.getValue('itemClass') && (
        <Badge variant='outline' className='text-center text-xs'>
          {row.getValue('itemClass')}
        </Badge>
      ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'unit',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Đơn vị tính' />,
    cell: ({ row }) => <div className='text-sm'>{row.getValue('unit')}</div>,
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'sideItems',
    header: ({ column }) => <CustomColumnHeader column={column} title='Món ăn kèm' defaultSort='desc' />,
    cell: ({ row }) => {
      const sideItems = row.getValue('sideItems') as string
      if (!sideItems) return <div>Món chính</div>

      const displayText = sideItems === 'Món ăn kèm' ? 'Món ăn kèm' : sideItems

      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className='max-w-[150px] truncate text-sm'>{displayText}</div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{displayText}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    },
    enableSorting: true,
    enableHiding: true
  },
  {
    accessorKey: 'city',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Thành phố' />,
    cell: ({ row }) => {
      const cityName = row.getValue('city') as string
      return <div className='text-sm'>{cityName}</div>
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'originalData',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Áp dụng tại' />,
    cell: ({ row }) => {
      const originalData = row.getValue('originalData') as any
      const hasStoreUid = originalData?.store_uid
      const hasCities = originalData?.cities && Array.isArray(originalData.cities) && originalData.cities.length > 0

      let applyLocation = 'Không xác định'
      if (hasStoreUid && !hasCities) {
        applyLocation = 'Cửa hàng'
      } else if (hasCities && !hasStoreUid) {
        applyLocation = 'Thành phố'
      } else if (hasStoreUid && hasCities) {
        applyLocation = 'Cửa hàng & Thành phố'
      }

      return <div className='text-sm'>{applyLocation}</div>
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'originalData',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Trạng thái' />,
    cell: ({ row }) => {
      const originalData = row.getValue('originalData') as any
      const applyWithStore = originalData?.apply_with_store || 0
      const isActive = originalData?.active || 0

      let statusText = 'Không xác định'
      if (isActive === 1) {
        statusText = 'Active'
      } else if (isActive === 0) {
        statusText = 'Không bán'
      }

      // Override with apply_with_store status if applicable
      if (applyWithStore === 2) {
        statusText = 'Món mới'
      } else if (applyWithStore === 1) {
        statusText = 'Sửa từ món gốc'
      }

      return <div className='text-sm'>{statusText}</div>
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'buffetConfig',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Cấu hình buffet' />,
    cell: ({ row }) => {
      const menuItem = row.original
      const buffetConfig = row.getValue('buffetConfig') as string
      const isBuffetConfigured = buffetConfig === 'Đã cấu hình'

      if (isBuffetConfigured) {
        return (
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium'>Đã cấu hình</span>
            <Button
              variant={'outline'}
              size='sm'
              onClick={e => {
                e.stopPropagation()
                onBuffetConfigClick(menuItem)
              }}
              className='h-6 px-2 text-xs'
            >
              <Settings className='h-3 w-3' />
            </Button>
          </div>
        )
      }

      return (
        <Button
          variant={'outline'}
          size='sm'
          onClick={e => {
            e.stopPropagation()
            onBuffetConfigClick(menuItem)
          }}
          className='h-7 px-2 text-xs'
        >
          <Settings className='mr-1 h-3 w-3' />
          Cấu hình
        </Button>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'customization',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Customization' />,
    cell: ({ row, table }) => {
      const menuItem = row.original
      const customization = row.getValue('customization') as string
      const meta = table.options.meta as {
        onCustomizationClick?: (menuItem: ItemsInStore) => void
        customizations?: Array<{ id: string; name: string }>
      }

      const currentCustomization = meta?.customizations?.find(c => c.id === customization)

      if (currentCustomization) {
        return (
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium'>{currentCustomization.name}</span>
            <Button
              variant={'outline'}
              size='sm'
              onClick={e => {
                e.stopPropagation()
                onCustomizationClick?.(menuItem)
              }}
              className='h-6 px-2 text-xs'
            >
              <Settings className='h-3 w-3' />
            </Button>
          </div>
        )
      }

      return (
        <Button
          variant={'outline'}
          size='sm'
          onClick={e => {
            e.stopPropagation()
            onCustomizationClick?.(menuItem)
          }}
          className='h-7 px-2 text-xs'
        >
          <Settings className='mr-1 h-3 w-3' />
          Cấu hình
        </Button>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    id: 'copy',
    header: 'Sao chép tạo món mới',
    cell: ({ row, table }) => {
      const menuItem = row.original
      const meta = table.options.meta as {
        onCopyClick?: (menuItem: ItemsInStore) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          className='ml-14 h-8 w-8'
          onClick={e => {
            e.stopPropagation()
            meta?.onCopyClick?.(menuItem)
          }}
        >
          <IconCopy className='h-4 w-4' />
          <span className='sr-only'>Sao chép món ăn {getItemName(row.original)}</span>
        </Button>
      )
    },
    enableSorting: false,
    enableHiding: true
  },
  {
    accessorKey: 'isActive',
    header: ({ column }) => <CustomColumnHeader column={column} title='Thao tác' defaultSort='desc' />,
    enableSorting: true,
    cell: ({ row, table }) => {
      const menuItem = row.original
      const isActive = getItemActive(menuItem)
      const meta = table.options.meta as {
        onToggleStatus?: (menuItem: ItemsInStore) => void
      }

      return (
        <div
          onClick={e => {
            e.stopPropagation()
            meta?.onToggleStatus?.(menuItem)
          }}
          className='cursor-pointer'
        >
          <StatusBadge isActive={isActive} activeText='Active' inactiveText='Deactive' />
        </div>
      )
    },
    enableHiding: true
  },
  {
    id: 'actions',
    cell: ({ row, table }) => {
      const menuItem = row.original
      const meta = table.options.meta as {
        onDeleteClick?: (menuItem: ItemsInStore) => void
      }

      return (
        <div className='flex items-center justify-center'>
          <Button
            variant='ghost'
            size='sm'
            onClick={e => {
              e.stopPropagation()
              meta?.onDeleteClick?.(menuItem)
            }}
            className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
          >
            <IconTrash className='h-4 w-4' />
          </Button>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
    size: 80
  }
]

// Default columns for backward compatibility
export const columns = columnsDefinition({ onBuffetConfigClick: () => {} })

// Function to create columns with custom handlers
export const createColumns = columnsDefinition

// Function to create table columns (same as createColumns, works for both types)
export const createTableColumns = columnsDefinition
