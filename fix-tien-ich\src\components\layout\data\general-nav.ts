import { IconChecklist, IconLayoutDashboard } from '@tabler/icons-react'

import { type NavItem } from '../types'

export const generalNavItems: NavItem[] = [
  {
    title: 'Trang Chủ',
    url: '/',
    icon: IconLayoutDashboard
  },
  {
    title: '<PERSON><PERSON><PERSON> Hàng',
    icon: IconChecklist,
    items: [
      {
        title: '<PERSON>h sách nhà hàng',
        url: '/setting/store'
      },
      {
        title: '<PERSON><PERSON><PERSON>ng thức thanh toán',
        url: '/setting/payment-method'
      },
      {
        title: '<PERSON>uồn đơn hàng',
        url: '/setting/source'
      },
      {
        title: '<PERSON><PERSON> trí máy in',
        items: [
          {
            title: '<PERSON><PERSON> trí máy in toàn thương hiệu',
            url: '/setting/printer-position/printer-position-in-brand'
          },
          {
            title: '<PERSON><PERSON> trí máy in tại cửa hàng',
            url: '/setting/printer-position/printer-position-in-store'
          }
        ]
      },
      {
        title: '<PERSON><PERSON> vực',
        url: '/setting/area'
      },
      {
        title: '<PERSON>à<PERSON>',
        url: '/setting/table'
      },
      {
        title: '<PERSON><PERSON> đồ bàn',
        url: '/setting/table-layout'
      },
      {
        title: 'Mẫu hoá đơn',
        url: '/setting/bill-model'
      },
      {
        title: 'Merchant Momo',
        url: '/setting/momo-merchant'
      },
      {
        title: 'Liên kết điểm bán hàng',
        url: '/setting/schedule-sale'
      }
    ]
  }
]
