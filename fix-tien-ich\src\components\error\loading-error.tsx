interface LoadingErrorProps {
  title?: string
  description?: string
}

export function LoadingError({
  title = 'Lỗi tải dữ liệu',
  description = 'Không thể tải thông tin customization'
}: LoadingErrorProps) {
  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='flex h-64 items-center justify-center'>
        <div className='text-center'>
          <div className='mb-4 text-lg text-red-600'>{title}</div>
          <div className='text-sm text-gray-600'>{description}</div>
        </div>
      </div>
    </div>
  )
}
