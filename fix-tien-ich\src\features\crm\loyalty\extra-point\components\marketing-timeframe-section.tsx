import { HelpCircle } from 'lucide-react'

import { <PERSON>, <PERSON><PERSON>, Too<PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from '@/components/ui'

const DAYS_OF_WEEK = [
  { label: 'CN', value: '6' }, // Sunday = 6
  { label: 'T2', value: '0' }, // Monday = 0
  { label: 'T3', value: '1' }, // Tuesday = 1
  { label: 'T4', value: '2' }, // Wednesday = 2
  { label: 'T5', value: '3' }, // Thursday = 3
  { label: 'T6', value: '4' }, // Friday = 4
  { label: 'T7', value: '5' } // Saturday = 5
]

const TIME_SLOTS = [
  { value: '0' },
  { value: '1' },
  { value: '2' },
  { value: '3' },
  { value: '4' },
  { value: '5' },
  { value: '6' },
  { value: '7' },
  { value: '8' },
  { value: '9' },
  { value: '10' },
  { value: '11' },
  { value: '12' },
  { value: '13' },
  { value: '14' },
  { value: '15' },
  { value: '16' },
  { value: '17' },
  { value: '18' },
  { value: '19' },
  { value: '20' },
  { value: '21' },
  { value: '22' },
  { value: '23' }
]

interface MarketingTimeframeSectionProps {
  marketingDays?: string[]
  marketingHours?: string[]
  onDaysChange?: (days: string[]) => void
  onHoursChange?: (hours: string[]) => void
  onTimeValuesChange?: (values: { time_date_week: number; time_hour_day: number }) => void
}

export function MarketingTimeframeSection({
  marketingDays = [],
  marketingHours = [],
  onDaysChange,
  onHoursChange,
  onTimeValuesChange
}: MarketingTimeframeSectionProps) {
  // Helper function to calculate time values using same logic as form
  const calculateTimeValues = (days: string[], hours: string[]) => {
    // Convert days: CN=2^1=2, T2=2^2=4, T3=2^3=8, T4=2^4=16, T5=2^5=32, T6=2^6=64, T7=2^7=128
    const dayValueMap: { [key: string]: number } = {
      '6': 2, // CN (Sunday) = 2^1 = 2
      '0': 4, // T2 (Monday) = 2^2 = 4
      '1': 8, // T3 (Tuesday) = 2^3 = 8
      '2': 16, // T4 (Wednesday) = 2^4 = 16
      '3': 32, // T5 (Thursday) = 2^5 = 32
      '4': 64, // T6 (Friday) = 2^6 = 64
      '5': 128 // T7 (Saturday) = 2^7 = 128
    }

    const time_date_week = days.reduce((sum, day) => {
      return sum + (dayValueMap[day] || 0)
    }, 0)

    const time_hour_day = hours.reduce((sum, hour) => {
      const hourIndex = parseInt(hour)
      return sum + Math.pow(2, hourIndex)
    }, 0)

    return { time_date_week, time_hour_day }
  }

  const handleDayToggle = (day: string) => {
    const updatedDays = marketingDays.includes(day) ? marketingDays.filter(d => d !== day) : [...marketingDays, day]

    onDaysChange?.(updatedDays)

    // Calculate and notify time values
    const timeValues = calculateTimeValues(updatedDays, marketingHours)
    onTimeValuesChange?.(timeValues)
  }

  const handleTimeToggle = (time: string) => {
    const updatedTimes = marketingHours.includes(time)
      ? marketingHours.filter(t => t !== time)
      : [...marketingHours, time]

    onHoursChange?.(updatedTimes)

    // Calculate and notify time values
    const timeValues = calculateTimeValues(marketingDays, updatedTimes)
    onTimeValuesChange?.(timeValues)
  }

  return (
    <TooltipProvider>
      <div className='space-y-4'>
        <div className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Label className='text-sm font-medium text-gray-500'>Chọn ngày</Label>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
              </TooltipTrigger>
              <TooltipContent className='max-w-xs'>
                <p className='text-sm'>Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần</p>
              </TooltipContent>
            </Tooltip>
          </div>

          <div className='flex gap-2'>
            {DAYS_OF_WEEK.map(day => (
              <Button
                key={day.value}
                type='button'
                variant={marketingDays.includes(day.value) ? 'default' : 'outline'}
                size='sm'
                onClick={() => handleDayToggle(day.value)}
                className='flex-1'
              >
                {day.label}
              </Button>
            ))}
          </div>
        </div>

        <div className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Label className='text-sm font-medium text-gray-500'>Chọn giờ</Label>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
              </TooltipTrigger>
              <TooltipContent className='max-w-xs'>
                <p className='text-sm'>Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày</p>
              </TooltipContent>
            </Tooltip>
          </div>

          <div className='grid grid-cols-6 gap-2'>
            {TIME_SLOTS.map(time => {
              const hour = parseInt(time.value)
              const nextHour = hour + 1
              const displayText = nextHour === 24 ? `${hour}h-24h` : `${hour}-${nextHour}h`

              return (
                <Button
                  key={time.value}
                  type='button'
                  variant={marketingHours.includes(time.value) ? 'default' : 'outline'}
                  size='sm'
                  onClick={() => handleTimeToggle(time.value)}
                  className='text-xs'
                >
                  {displayText}
                </Button>
              )
            })}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
