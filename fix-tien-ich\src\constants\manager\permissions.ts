import type { PermissionSection } from './types'

export const MANAGER_PERMISSIONS: PermissionSection[] = [
  {
    id: 'pin',
    title: 'PIN',
    items: [
      {
        id: 'pin-management',
        label: '<PERSON> phép hiển thị, quản lý mã PIN tại app MANAGER',
        permissions: ['MANAGER.PIN']
      }
    ]
  },
  {
    id: 'reports',
    title: 'Báo cáo',
    items: [
      {
        id: 'report-management',
        label: 'Quản lý Báo cáo',
        permissions: ['MANAGER_REPORT.VIEW']
      }
    ]
  },
  {
    id: 'accounting-vo',
    title: 'Kế Toán Vo',
    items: [
      {
        id: 'accounting-vo-management',
        label: 'Quản lý Kế toán Vo',
        permissions: [
          'MANAGER_KTV.VIEW',
          'MANAGER_KTV.FUND_VIEW',
          'MANAGER_KTV.REPORT_VIEW',
          'MANAGER_KTV.SUPPLIER_VIEW'
        ]
      }
    ]
  },
  {
    id: 'marketplace',
    title: 'Marketplace',
    items: [
      {
        id: 'marketplace-management',
        label: 'Quản lý Marketplace',
        permissions: ['MANAGER_MARKET.VIEW']
      }
    ]
  },
  {
    id: 'online-store',
    title: 'CH online',
    items: [
      {
        id: 'online-store-management',
        label: 'Quản lý CH online',
        permissions: ['MANAGER_STORE.VIEW']
      }
    ]
  },
  {
    id: 'menu',
    title: 'Thực đơn',
    items: [
      {
        id: 'menu-management',
        label: 'Quản lý thực đơn',
        permissions: ['MANAGER_ITEM.VIEW']
      }
    ]
  }
]
