import { useState } from 'react'

import { IconDownload } from '@tabler/icons-react'

import { RemovedItem, CityData } from '@/types/item-removed'
import { toast } from 'sonner'

import { getErrorMessage } from '@/utils/error-utils'

import { Button, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

import { exportRemovedItemsReportByCity } from '../utils'

interface ActionBarProps {
  searchQuery: string
  onSearchQueryChange: (value: string) => void
  onSearchSubmit: (searchTerm: string) => void
  selectedCityId: string
  onCityChange: (cityId: string) => void
  cities: CityData[]
  removedItems: RemovedItem[]
}

export function ActionBar({
  searchQuery,
  onSearchQueryChange,
  onSearchSubmit,
  selectedCityId,
  onCityChange,
  cities,
  removedItems
}: ActionBarProps) {
  const [isExporting, setIsExporting] = useState(false)

  const handleExportReport = async () => {
    try {
      setIsExporting(true)

      const filteredItems =
        selectedCityId === 'all'
          ? removedItems || []
          : (removedItems || []).filter(item => item.city_uid === selectedCityId)

      if (filteredItems.length === 0) {
        toast.error('Không có dữ liệu để xuất báo cáo')
        return
      }

      await exportRemovedItemsReportByCity(filteredItems, cities)
      toast.success('Báo cáo đã được xuất thành công!')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    } finally {
      setIsExporting(false)
    }
  }
  return (
    <div className='mb-2 flex items-center justify-between'>
      <div className='flex items-center gap-4'>
        <h2 className='text-xl font-semibold'>Món đã xoá</h2>
        <Input
          placeholder='Tìm kiếm món đã xóa...'
          className='w-64'
          value={searchQuery}
          onChange={e => onSearchQueryChange(e.target.value)}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              e.preventDefault()
              onSearchSubmit(searchQuery)
            }
          }}
        />
        <Select value={selectedCityId} onValueChange={onCityChange}>
          <SelectTrigger className='w-48'>
            <SelectValue placeholder='Chọn thành phố' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả các thành phố</SelectItem>
            {cities.map(city => (
              <SelectItem key={city.id} value={city.id}>
                {city.city_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <Button size='sm' onClick={handleExportReport} disabled={isExporting}>
        <IconDownload className='mr-2 h-4 w-4' />
        {isExporting ? 'Đang xuất...' : 'Xuất báo cáo'}
      </Button>
    </div>
  )
}
