import { useState, useMemo } from 'react'

import { ChevronDown, Search } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

export interface MultiSelectOption {
  value: string
  label: string
  disabled?: boolean
}

interface MultiSelectDropdownProps {
  options: MultiSelectOption[]
  value: string[]
  onValueChange: (value: string[]) => void
  placeholder?: string
  searchPlaceholder?: string
  className?: string
  disabled?: boolean
  isLoading?: boolean
  showSelectAll?: boolean
  selectAllLabel?: string
  emptyText?: string
  loadingText?: string
}

export function MultiSelectDropdown({
  options,
  value,
  onValueChange,
  placeholder = 'Chọn...',
  searchPlaceholder = 'Tìm kiếm...',
  className,
  disabled = false,
  isLoading = false,
  showSelectAll = true,
  selectAllLabel = 'Chọn tất cả',
  emptyText = 'Không có dữ liệu',
  loadingText = 'Đang tải...'
}: MultiSelectDropdownProps) {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredOptions = useMemo(() => {
    if (!searchTerm) return options
    return options.filter(option => option.label.toLowerCase().includes(searchTerm.toLowerCase()))
  }, [options, searchTerm])

  const isAllSelected = useMemo(() => {
    if (filteredOptions.length === 0) return false
    return filteredOptions.every(option => option.disabled || value.includes(option.value))
  }, [filteredOptions, value])

  const isSomeSelected = useMemo(() => {
    return filteredOptions.some(option => value.includes(option.value))
  }, [filteredOptions, value])

  const handleSelectAll = () => {
    if (isAllSelected) {
      const filteredValues = filteredOptions.map(option => option.value)
      const newValue = value.filter(v => !filteredValues.includes(v))
      onValueChange(newValue)
    } else {
      const filteredValues = filteredOptions.filter(option => !option.disabled).map(option => option.value)
      const newValue = [...new Set([...value, ...filteredValues])]
      onValueChange(newValue)
    }
  }

  const handleOptionToggle = (optionValue: string) => {
    if (value.includes(optionValue)) {
      onValueChange(value.filter(v => v !== optionValue))
    } else {
      onValueChange([...value, optionValue])
    }
  }

  const getDisplayText = () => {
    if (value.length === 0) return placeholder
    if (value.length === 1) {
      const selectedOption = options.find(opt => opt.value === value[0])
      return selectedOption?.label || placeholder
    }
    return `Đã chọn ${value.length} mục`
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className={cn('w-full justify-between', value.length === 0 && 'text-muted-foreground', className)}
          disabled={disabled}
        >
          <span className='truncate'>{getDisplayText()}</span>
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[var(--radix-popover-trigger-width)] p-0' align='start'>
        <div className='flex flex-col'>
          <div className='flex items-center border-b px-3 py-2'>
            <Search className='mr-2 h-4 w-4 shrink-0 opacity-50' />
            <Input
              placeholder={searchPlaceholder}
              onChange={e => setSearchTerm(e.target.value)}
              className='border-0 p-0 focus-visible:ring-0'
            />
          </div>

          <div className='max-h-60 overflow-auto'>
            {isLoading ? (
              <div className='text-muted-foreground flex items-center justify-center py-6 text-sm'>{loadingText}</div>
            ) : filteredOptions.length === 0 ? (
              <div className='text-muted-foreground flex items-center justify-center py-6 text-sm'>{emptyText}</div>
            ) : (
              <>
                {showSelectAll && filteredOptions.length > 1 && (
                  <div className='border-b'>
                    <div
                      className='hover:bg-accent flex cursor-pointer items-center space-x-2 px-3 py-2'
                      onClick={handleSelectAll}
                    >
                      <Checkbox
                        checked={isAllSelected}
                        data-state={!isAllSelected && isSomeSelected ? 'indeterminate' : undefined}
                        onChange={() => {}}
                      />
                      <span className='text-sm font-medium'>{selectAllLabel}</span>
                    </div>
                  </div>
                )}

                {filteredOptions.map(option => (
                  <div
                    key={option.value}
                    className={cn(
                      'hover:bg-accent flex cursor-pointer items-center space-x-2 px-3 py-2',
                      option.disabled && 'cursor-not-allowed opacity-50'
                    )}
                    onClick={() => !option.disabled && handleOptionToggle(option.value)}
                  >
                    <Checkbox checked={value.includes(option.value)} disabled={option.disabled} onChange={() => {}} />
                    <span className='text-sm'>{option.label}</span>
                  </div>
                ))}
              </>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
