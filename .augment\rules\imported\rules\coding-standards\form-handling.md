---
type: "always_apply"
---

# Form Handling Standards

## Zod Schema Definition
- **Use Zod schemas** for all form validation and type safety
- **Define clear validation rules** with appropriate error messages
- **Export TypeScript types** using `z.infer<typeof schema>`
- **Use Vietnamese error messages** for user-facing validation

## React Hook Form Integration
- **Use React Hook Form** with Zod resolver for all form handling
- **Set appropriate default values** for form initialization
- **Handle form submission** with proper error handling
- **Return form state** including validation status and errors

## Form Component Pattern
- **Use shadcn/ui Form components** for consistent styling
- **Implement FormField** for all form inputs with proper validation
- **Show validation errors** using FormMessage components
- **Handle loading states** during form submission

## Form Validation Rules
- **Required fields**: Use appropriate Zod validators with Vietnamese error messages
- **Field validation**: Implement real-time validation with proper error display
- **Cross-field validation**: Use Zod refinements for complex validation logic
- **Async validation**: Implement server-side validation for unique constraints

## Form State Management
- **Use React Hook Form state**: Avoid additional useState for form data
- **Controlled components**: Use FormField for all form inputs
- **Reset handling**: Implement proper form reset functionality
