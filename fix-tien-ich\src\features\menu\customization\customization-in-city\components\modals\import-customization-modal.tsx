import { IconDownload, IconUpload } from '@tabler/icons-react'

import { PosModal } from '@/components/pos'
import { Button } from '@/components/ui'
import { ParsedCustomizationData } from '@/types/customizations'

interface ImportCustomizationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  showImportParsedData: boolean
  importSelectedFile: File | null
  importParsedData: ParsedCustomizationData[]
  isLoading: boolean
  onCancel: () => void
  onConfirm: () => void
  onDownloadTemplate: () => void
  onImportFileUpload: () => void
}

export function ImportCustomizationModal({
  open,
  onOpenChange,
  showImportParsedData,
  importSelectedFile,
  importParsedData,
  isLoading,
  onCancel,
  onConfirm,
  onDownloadTemplate,
  onImportFileUpload,
}: ImportCustomizationModalProps) {
  return (
    <PosModal
      title='Thêm customization từ file'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      confirmText={showImportParsedData ? 'Lưu' : 'Tiếp tục'}
      cancelText='Hủy'
      centerTitle={true}
      maxWidth={showImportParsedData ? 'sm:max-w-6xl' : 'sm:max-w-[400px]'}
      isLoading={isLoading}
    >
      <div className='space-y-4'>
        {!showImportParsedData && (
          <>
            <div className='rounded-md border p-3'>
              <div className='mb-3 font-medium'>Bước 1. Tải file mẫu</div>
              <div className='flex items-center justify-between'>
                <div className='flex flex-col'>
                  <span className='text-sm text-gray-600'>
                    Tải xuống file mẫu để tạo customization
                  </span>
                </div>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={onDownloadTemplate}
                  className='h-8 w-8 p-0'
                >
                  <IconDownload className='h-4 w-4' />
                </Button>
              </div>
            </div>

            <div className='rounded-md border p-3'>
              <div className='mb-3 font-medium'>Bước 2. Thêm món vào file</div>
              <div className='rounded-md bg-yellow-50 p-3 text-sm text-yellow-800'>
                <strong>Không được để trống các cột:</strong> Tên, Thành phố, Tên nhóm, Mã món
                theo nhóm.
              </div>
            </div>

            <div className='rounded-md border p-3'>
              <div className='mb-3 font-medium'>Bước 3. Tải file thực đơn lên</div>
              <div className='flex items-center justify-between'>
                <div className='flex flex-col'>
                  <span className='text-xs text-gray-600'>
                    Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên
                  </span>
                  {importSelectedFile && (
                    <span className='mt-1 text-xs text-gray-500'>
                      File đã chọn: {importSelectedFile.name}
                    </span>
                  )}
                </div>
                <Button
                  size='sm'
                  variant='default'
                  onClick={onImportFileUpload}
                  className='h-8 w-8 p-0'
                >
                  <IconUpload className='h-4 w-4' />
                </Button>
              </div>
            </div>
          </>
        )}

        {showImportParsedData && (
          <div className='max-h-60 overflow-y-auto rounded-md border'>
            <table className='w-full text-sm'>
              <thead className='bg-gray-50'>
                <tr>
                  <th className='border-b px-3 py-2 text-left'>Tên</th>
                  <th className='border-b px-3 py-2 text-left'>Thành phố</th>
                  <th className='border-b px-3 py-2 text-left'>Mã món áp dụng</th>
                  <th className='border-b px-3 py-2 text-left'>Tên nhóm</th>
                  <th className='border-b px-3 py-2 text-left'>Yêu cầu chọn</th>
                  <th className='border-b px-3 py-2 text-left'>Giới hạn chọn</th>
                  <th className='border-b px-3 py-2 text-left'>Mã món theo nhóm</th>
                </tr>
              </thead>
              <tbody>
                {importParsedData.map((item, index) => (
                  <tr key={index} className='border-b'>
                    <td className='px-3 py-2'>{item.name}</td>
                    <td className='px-3 py-2'>{item.cityName}</td>
                    <td className='px-3 py-2'>{item.appliedItemCodes}</td>
                    <td className='px-3 py-2'>{item.groupName}</td>
                    <td className='px-3 py-2'>{item.minRequired}</td>
                    <td className='px-3 py-2'>{item.maxAllowed}</td>
                    <td className='px-3 py-2'>{item.groupItemCodes}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </PosModal>
  )
}
