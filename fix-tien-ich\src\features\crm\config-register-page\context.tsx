import React, { createContext, useContext } from 'react'

import { useCurrentBrand } from '@/stores'
import type { RegisterPageFormField, SaveRegisterPageConfigRequest, PosParentSettings } from '@/types/api/crm'
import { toast } from 'sonner'

import { useRegisterPageConfig, useSaveRegisterPageConfig, usePosParentSettings } from '@/hooks/crm'

const DEFAULT_FIELDS: RegisterPageFormField[] = [
  { field_id: 'phone', field_name: 'Số điện thoại', active: 1, data_type: 'string', require: true },
  { field_id: 'name', field_name: 'Họ và tên', active: 1, data_type: 'string', require: true },
  { field_id: 'gender', field_name: 'Giới tính', active: 0, data_type: 'string', require: false },
  { field_id: 'birthday', field_name: '<PERSON><PERSON><PERSON> sinh', active: 0, data_type: 'string', require: false },
  { field_id: 'address', field_name: 'Địa chỉ', active: 0, data_type: 'string', require: false },
  { field_id: 'email', field_name: '<PERSON><PERSON>', active: 0, data_type: 'string', require: false }
]

export type OtpMethod = 'sms' | 'zns' | 'none'

export interface ConfigRegisterContextValue {
  brandId: string
  active: boolean
  setActive: (val: boolean) => void
  logo: string
  setLogo: (val: string) => void
  banner: string
  setBanner: (val: string) => void
  defaultLang: string
  setDefaultLang: (val: string) => void
  otpMethod: OtpMethod
  setOtpMethod: (val: OtpMethod) => void
  fields: RegisterPageFormField[]
  updateField: (id: string, partial: Partial<RegisterPageFormField>) => void
  isLoading?: boolean
  isSaving?: boolean
  onSave: () => void
  settings?: PosParentSettings
  isSettingsLoading?: boolean
}

const ConfigRegisterContext = createContext<ConfigRegisterContextValue | undefined>(undefined)

export function ConfigRegisterProvider({ children }: { children: React.ReactNode }) {
  const { selectedBrand } = useCurrentBrand()
  const brandId = selectedBrand?.brandId || ''

  const { data, isLoading } = useRegisterPageConfig({ pos_parent: brandId })
  const { data: settingsData, isLoading: isSettingsLoading } = usePosParentSettings({ pos_parent: brandId })
  const saveMutation = useSaveRegisterPageConfig()

  const [active, setActive] = React.useState<boolean>(true)
  const [logo, setLogo] = React.useState<string>('')
  const [banner, setBanner] = React.useState<string>('')
  const [defaultLang, setDefaultLang] = React.useState<string>('vi')
  const [otpMethod, setOtpMethod] = React.useState<OtpMethod>('none')
  const [fields, setFields] = React.useState<RegisterPageFormField[]>(DEFAULT_FIELDS)

  React.useEffect(() => {
    if (data?.data) {
      const cfg = data.data
      setActive(cfg.active === 1)
      setLogo(cfg.logo !== '' ? cfg.logo : settingsData?.Logo_Image || '')
      setBanner(cfg.banner !== '' ? cfg.banner : settingsData?.image || '')
      setDefaultLang(cfg.default_lang || 'vi')

      const initialOtp: OtpMethod = cfg.send_otp === 1 ? 'sms' : 'none'
      setOtpMethod(initialOtp)

      if (cfg.form_data && cfg.form_data.length > 0) {
        const serverFieldsMap = new Map(cfg.form_data.map(f => [f.field_id, f]))

        const mergedFields = DEFAULT_FIELDS.map(defaultField => {
          const serverField = serverFieldsMap.get(defaultField.field_id)
          if (serverField) {
            serverFieldsMap.delete(defaultField.field_id)
            return { ...defaultField, ...serverField }
          }
          return defaultField
        })

        serverFieldsMap.forEach(newField => mergedFields.push(newField))

        setFields(mergedFields)
      } else {
        setFields(DEFAULT_FIELDS)
      }
    }
  }, [data, settingsData])

  const updateField = (id: string, partial: Partial<RegisterPageFormField>) => {
    setFields(prev => prev.map(f => (f.field_id === id ? { ...f, ...partial } : f)))
  }

  const onSave = () => {
    if (!brandId) {
      toast.error('Chưa chọn thương hiệu')
      return
    }

    const payload: SaveRegisterPageConfigRequest = {
      pos_parent: brandId,
      banner,
      active: active ? 1 : 0,
      logo,
      send_otp: otpMethod === 'none' ? 0 : 1,
      default_lang: defaultLang || 'vi',
      form_data: fields
    }

    saveMutation.mutate(payload, {
      onSuccess: () => {
        toast.success('Lưu cấu hình thành công')
      },
      onError: (err: any) => {
        toast.error(err?.message || 'Lưu cấu hình thất bại')
      }
    })
  }

  const value = {
    brandId,
    active,
    setActive,
    logo,
    setLogo,
    banner,
    setBanner,
    defaultLang,
    setDefaultLang,
    otpMethod,
    setOtpMethod,
    fields,
    updateField,
    isLoading,
    isSaving: saveMutation.isPending,
    onSave,
    settings: settingsData,
    isSettingsLoading
  }

  return <ConfigRegisterContext.Provider value={value}>{children}</ConfigRegisterContext.Provider>
}

export function useConfigRegister() {
  const ctx = useContext(ConfigRegisterContext)
  if (!ctx) throw new Error('useConfigRegister must be used within ConfigRegisterProvider')
  return ctx
}
