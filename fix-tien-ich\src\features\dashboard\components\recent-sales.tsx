import { useMemo } from 'react'

import { useDashboardContext } from '../context/dashboard-context'

export function RecentSales() {
  const { itemsData, isItemsLoading, itemsError, itemsSortBy } = useDashboardContext()

  const { topProducts, remainingAmount } = useMemo(() => {
    if (!itemsData.length) {
      return { topProducts: [], remainingAmount: '0 ₫' }
    }

    const top5Items = itemsData.slice(0, 5)

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('vi-VN').format(amount) + ' ₫'
    }

    const getDisplayValue = (item: (typeof top5Items)[0]) => {
      switch (itemsSortBy) {
        case 'quantity_sold':
          return item.quantity_sold.toFixed(2)
        case 'revenue_gross':
          return formatCurrency(item.revenue_gross)
        case 'revenue_net':
        default:
          return formatCurrency(item.revenue_net)
      }
    }

    const topProducts = top5Items.map(item => ({
      name: item.item_name,
      price: getDisplayValue(item),
      quantity: item.quantity_sold,
      unit: item.unit_name
    }))

    const getItemValue = (item: (typeof itemsData)[0]) => {
      switch (itemsSortBy) {
        case 'quantity_sold':
          return item.quantity_sold
        case 'revenue_gross':
          return item.revenue_gross
        case 'revenue_net':
        default:
          return item.revenue_net
      }
    }

    const top5Total = top5Items.reduce((sum, item) => sum + getItemValue(item), 0)
    const grandTotal = itemsData.reduce((sum, item) => sum + getItemValue(item), 0)
    const remainingTotal = grandTotal - top5Total

    const remainingAmount = itemsSortBy === 'quantity_sold' ? remainingTotal.toFixed(2) : formatCurrency(remainingTotal)

    return {
      topProducts,
      remainingAmount
    }
  }, [itemsData, itemsSortBy])

  if (isItemsLoading) {
    return (
      <div className='flex h-[200px] items-center justify-center'>
        <div className='text-muted-foreground text-sm'>Đang tải dữ liệu...</div>
      </div>
    )
  }

  if (itemsError) {
    return (
      <div className='flex h-[200px] items-center justify-center'>
        <div className='text-sm text-red-500'>Lỗi: {itemsError}</div>
      </div>
    )
  }

  if (!topProducts.length) {
    return (
      <div className='flex h-[200px] items-center justify-center'>
        <div className='text-muted-foreground text-sm'>Chưa có thông tin</div>
      </div>
    )
  }

  return (
    <div className='space-y-5'>
      {topProducts.map((product, index) => (
        <div key={index} className='flex items-center gap-4'>
          <div className='w-4 text-lg font-bold'>{index + 1}</div>
          <div className='flex flex-1 items-center justify-between'>
            <p className='line-clamp-1 max-w-[200px] text-lg font-medium'>{product.name}</p>
            <div className='text-lg font-medium'>{product.price}</div>
          </div>
        </div>
      ))}

      <div className='flex items-center gap-4'>
        <div className='w-4'></div>
        <div className='flex flex-1 items-center justify-between'>
          <p className='text-muted-foreground line-clamp-1 max-w-[200px] text-lg font-medium'>Mặt hàng còn lại</p>
          <div className='text-muted-foreground text-lg font-medium'>{remainingAmount}</div>
        </div>
      </div>
    </div>
  )
}
