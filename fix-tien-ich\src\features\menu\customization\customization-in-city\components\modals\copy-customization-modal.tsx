import { Customization } from '@/types/customizations'

import { Label } from '@/components/ui/label'

import { PosModal } from '@/components/pos'
import { Input } from '@/components/ui'

interface CopyCustomizationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedCustomization: Customization | null
  copyName: string
  onCopyNameChange: (name: string) => void
  onCancel: () => void
  onConfirm: () => void
  isLoading: boolean
}

export function CopyCustomizationModal({
  open,
  onOpenChange,
  copyName,
  onCopyNameChange,
  onCancel,
  onConfirm,
  isLoading
}: CopyCustomizationModalProps) {
  return (
    <PosModal
      title='Sao chép customization'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      confirmText='Sao chép'
      cancelText='Hủy'
      isLoading={isLoading}
    >
      <div className='space-y-4'>
        <div className='flex items-center gap-4'>
          <Label htmlFor='copy-name' className='min-w-[120px] text-sm font-medium'>
            Tên customization <span className='text-red-500'>*</span>
          </Label>
          <Input
            id='copy-name'
            value={copyName}
            onChange={e => onCopyNameChange(e.target.value)}
            placeholder='Nhập tên customization'
            className='flex-1'
          />
        </div>
      </div>
    </PosModal>
  )
}
