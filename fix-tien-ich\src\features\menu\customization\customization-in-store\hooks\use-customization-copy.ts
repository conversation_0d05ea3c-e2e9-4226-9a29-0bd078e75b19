import { useState } from 'react'

import { Customization } from '@/types/customizations'
import { toast } from 'sonner'

import { useCopyCustomization } from '@/hooks/api'

export function useCustomizationCopy() {
  const [copyName, setCopyName] = useState('')
  const copyCustomizationMutation = useCopyCustomization()

  const initializeCopyName = (customization: Customization) => {
    setCopyName(`${customization.name} - Copy`)
  }

  const resetCopyName = () => {
    setCopyName('')
  }

  const handleCopyCustomization = async (customization: Customization, targetStoreUid?: string) => {
    if (!copyName.trim()) {
      toast.error('Vui lòng nhập tên customization')
      return false
    }

    try {
      await copyCustomizationMutation.mutateAsync({
        customizationId: customization.id,
        newName: copyName.trim(),
        targetStoreUid: targetStoreUid
      })

      toast.success('Sao chép customization thành công')
      resetCopyName()
      return true
    } catch (error) {
      console.error('Copy customization error:', error)
      toast.error('<PERSON><PERSON> lỗi xảy ra khi sao chép customization')
      return false
    }
  }

  return {
    copyName,
    setCopyName,
    initializeCopyName,
    resetCopyName,
    handleCopyCustomization,
    isLoading: copyCustomizationMutation.isPending
  }
}
