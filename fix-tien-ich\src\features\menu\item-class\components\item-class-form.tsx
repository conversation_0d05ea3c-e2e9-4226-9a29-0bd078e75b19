import { useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { X } from 'lucide-react'
import { toast } from 'sonner'

import { getErrorMessage } from '@/utils/error-utils'

import {
  useCreateItemClass,
  useItemClassData,
  useUpdateItemClass,
  useItemsData,
  useCitiesData
} from '@/hooks/api'

import { Button, Checkbox, Input, Label } from '@/components/ui'

import { ItemSelectionModal } from './item-selection-modal'

interface CreateItemClassFormProps {
  id?: string
}

export function CreateItemClassForm({ id }: CreateItemClassFormProps) {
  const navigate = useNavigate()
  const isEditMode = !!id

  const { mutate: createItemClass, isPending: isCreating } = useCreateItemClass()
  const { mutate: updateItemClass, isPending: isUpdating } = useUpdateItemClass()
  const { data: itemClassData, isLoading: isLoadingItemClass } = useItemClassData(
    id || '',
    isEditMode
  )
  const { data: cities = [] } = useCitiesData()

  const { data: items = [] } = useItemsData({
    params: {
      skip_limit: true,
      list_city_uid: cities.map(city => city.id).join(',')
    },
    enabled: cities.length > 0
  })

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    autoGenerateCode: true,
    selectedItems: [] as string[]
  })

  const [showItemModal, setShowItemModal] = useState(false)

  // Populate form data when in edit mode
  useEffect(() => {
    if (isEditMode && itemClassData) {
      setFormData({
        name: itemClassData.item_class_name,
        code: itemClassData.item_class_id,
        autoGenerateCode: false,
        selectedItems: itemClassData.list_item || []
      })
    }
  }, [isEditMode, itemClassData])

  const handleBack = () => {
    navigate({ to: '/menu/item-class' })
  }

  const handleSave = async () => {
    if (!isFormValid) return

    if (isEditMode && itemClassData) {
      // Update existing item class
      const updatedItemClass = {
        ...itemClassData,
        item_class_name: formData.name,
        item_class_id: formData.code,
        list_item: formData.selectedItems
      }

      updateItemClass(updatedItemClass, {
        onSuccess: () => {
          navigate({ to: '/menu/item-class' })
        }
      })
    } else {
      // Create new item class
      const itemClassData = {
        item_class_name: formData.name,
        item_class_id: formData.autoGenerateCode ? undefined : formData.code,
        list_item: formData.selectedItems
      }

      createItemClass(itemClassData, {
        onSuccess: () => {
          navigate({ to: '/menu/item-class' })
        }
      })
    }
  }

  const handleToggleStatus = async () => {
    if (!isEditMode || !itemClassData) return

    try {
      const updatedItemClass = {
        ...itemClassData,
        active: itemClassData.active === 1 ? 0 : 1
      }

      await updateItemClass(updatedItemClass, {
        onSuccess: () => {
          const statusText = updatedItemClass.active === 1 ? 'kích hoạt' : 'vô hiệu hóa'
          toast.success(`Đã ${statusText} loại món "${itemClassData.item_class_name}"`)
        }
      })
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const isFormValid = formData.name.trim() !== ''
  const isLoading = isCreating || isUpdating || isLoadingItemClass
  const isItemClassActive = itemClassData?.active === 1

  const handleItemSelection = () => {
    setShowItemModal(true)
  }

  const handleItemsSelected = (selectedItemIds: string[]) => {
    setFormData({ ...formData, selectedItems: selectedItemIds })
    setShowItemModal(false)
  }

  const getSelectedItemsDisplay = () => {
    if (formData.selectedItems.length === 0) {
      return 'Chọn món áp dụng'
    }

    return `${formData.selectedItems.length} món`
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header */}
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <div className='flex items-center gap-2'>
            {isEditMode && itemClassData && (
              <Button
                type='button'
                variant={isItemClassActive ? 'destructive' : 'default'}
                disabled={isLoading}
                className='min-w-[100px]'
                onClick={handleToggleStatus}
              >
                {isItemClassActive ? 'Deactivate' : 'Activate'}
              </Button>
            )}
            {(!isEditMode || isItemClassActive) && (
              <Button
                type='button'
                disabled={isLoading || !isFormValid}
                className='min-w-[100px]'
                onClick={handleSave}
              >
                {isLoading ? (isEditMode ? 'Đang cập nhật...' : 'Đang tạo...') : 'Lưu'}
              </Button>
            )}
          </div>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>
            {isEditMode ? 'Chỉnh sửa loại món' : 'Tạo loại món'}
          </h1>
        </div>
      </div>

      {/* Form Content */}
      <div className='mx-auto max-w-4xl'>
        <div className='p-6'>
          <div className='space-y-6'>
            {/* Section Title */}
            <div>
              <h2 className='text-lg font-medium text-gray-900'>Thông tin chi tiết</h2>
            </div>

            {/* Tên loại món */}
            <div className='flex items-center gap-4'>
              <Label htmlFor='item-class-name' className='min-w-[200px] text-sm font-medium'>
                Tên loại món <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='item-class-name'
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                placeholder='Nhập tên loại món'
                className='flex-1'
                disabled={isEditMode}
              />
            </div>

            {/* Mã loại món */}
            <div className='flex items-center gap-4'>
              <Label htmlFor='item-class-code' className='min-w-[200px] text-sm font-medium'>
                Mã loại món
              </Label>
              <Input
                id='item-class-code'
                value={formData.code}
                onChange={e => setFormData({ ...formData, code: e.target.value })}
                placeholder='Nếu để trống, hệ thống sẽ tự động tạo một mã loại món'
                disabled={formData.autoGenerateCode || isEditMode}
                className='flex-1'
              />
              {!isEditMode && (
                <Checkbox
                  id='auto-generate-code'
                  checked={formData.autoGenerateCode}
                  onCheckedChange={checked =>
                    setFormData({
                      ...formData,
                      autoGenerateCode: checked as boolean,
                      code: checked ? '' : formData.code
                    })
                  }
                />
              )}
            </div>

            {/* Item Assignment Section */}
            <div className='space-y-4'>
              <div>
                <h3 className='text-lg font-medium text-gray-900'>Thêm các món vào loại món</h3>
                <p className='text-sm text-gray-600'>
                  Các món đang thuộc loại món khác sẽ được gán lại vào loại món này.
                </p>
              </div>

              {/* Áp dụng cho món */}
              <div className='flex items-center gap-4'>
                <Label className='min-w-[200px] text-sm font-medium'>Áp dụng cho món</Label>
                <div className='flex-1'>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={handleItemSelection}
                    className='w-full justify-start text-left'
                  >
                    {getSelectedItemsDisplay()}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Item Selection Modal */}
      <ItemSelectionModal
        open={showItemModal}
        onOpenChange={setShowItemModal}
        items={items}
        selectedItems={formData.selectedItems}
        onItemsSelected={handleItemsSelected}
      />
    </div>
  )
}
