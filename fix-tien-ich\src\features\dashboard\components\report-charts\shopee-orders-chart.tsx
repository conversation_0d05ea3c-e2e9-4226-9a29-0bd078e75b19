import { useMemo } from 'react'

import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui'

import { useDashboardContext } from '../../context'

export function ShopeeOrdersChart() {
  const { sourcesData, isSourcesLoading, sourcesError, defaultDateRange } = useDashboardContext()

  const formatDateRange = useMemo(() => {
    const formatDate = (date: Date) => {
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`
    }

    return `${formatDate(defaultDateRange.from)} - ${formatDate(defaultDateRange.to)}`
  }, [defaultDateRange])

  const shopeeData = useMemo(() => {
    if (!sourcesData || sourcesData.length === 0) {
      return null
    }

    return sourcesData.find(source => source.source_name === 'ShopeeFood')
  }, [sourcesData])

  const detailData = useMemo(() => {
    if (!shopeeData) return []

    return [
      { label: 'Tổng giá trị đơn hàng', amount: shopeeData.revenue_gross },
      { label: 'Tổng hoa hồng chiết khấu', amount: shopeeData.commission_amount },
      { label: 'Tổng khuyến mại nhà hàng', amount: shopeeData.discount_amount },
      { label: 'Tổng giảm giá đối tác', amount: shopeeData.partner_marketing_amount }
    ]
  }, [shopeeData])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount) + ' ₫'
  }

  if (isSourcesLoading) {
    return (
      <Card className='col-span-1'>
        <CardHeader>
          <CardTitle>Đơn từ kênh: Shopee</CardTitle>
          <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='text-muted-foreground py-8 text-center'>Đang tải dữ liệu...</div>
        </CardContent>
      </Card>
    )
  }

  if (sourcesError) {
    return (
      <Card className='col-span-1'>
        <CardHeader>
          <CardTitle>Đơn từ kênh: Shopee</CardTitle>
          <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='py-8 text-center text-red-500'>Lỗi: {sourcesError}</div>
        </CardContent>
      </Card>
    )
  }

  if (!shopeeData) {
    return (
      <Card className='col-span-1'>
        <CardHeader>
          <CardTitle>Đơn từ kênh: Shopee</CardTitle>
          <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex h-[200px] items-center justify-center'>
            <div className='text-muted-foreground text-sm'>Chưa có thông tin</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className='col-span-1'>
      <CardHeader>
        <CardTitle>Đơn từ kênh: Shopee</CardTitle>
        <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
        <CardAction>
          {/* <button className='cursor-pointer text-xs text-blue-600 hover:text-blue-800'>Chi tiết</button> */}
        </CardAction>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          <div className='text-center'>
            <div className='text-muted-foreground text-xs'>Tổng đơn</div>
            <div className='text-lg font-bold'>{shopeeData.total_bill.toLocaleString()}</div>
          </div>
          <div className='text-center'>
            <div className='text-muted-foreground text-xs'>Tổng thực thu</div>
            <div className='text-lg font-bold'>{formatCurrency(shopeeData.revenue_net)}</div>
          </div>
        </div>

        <div className='space-y-2'>
          <div className='flex items-center justify-between border-b-2 border-gray-200 pb-2'>
            <span className='text-xs font-semibold text-gray-900'>Danh mục</span>
            <span className='text-xs font-semibold text-gray-900'>Số tiền</span>
          </div>

          {detailData.map((item, index) => (
            <div
              key={index}
              className='flex items-center justify-between border-b border-gray-100 py-2 last:border-b-0'
            >
              <span className='text-xs text-gray-700'>{item.label}</span>
              <span className='text-xs font-medium'>{formatCurrency(item.amount)}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
