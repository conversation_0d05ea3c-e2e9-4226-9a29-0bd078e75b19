import { Pencil } from 'lucide-react'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui'

import { useMembershipTypeContext } from '../context'
import { formatPercentage, formatUpgradeCondition, formatReviewCondition, formatTime, getStatusInfo } from '../util'

export function MembershipTypeTable() {
  const { data, isLoading, error, toggleActivation, setEditDialogOpen, setSelectedMembershipType } =
    useMembershipTypeContext()

  if (isLoading) {
    return (
      <div className='flex items-center justify-center py-8'>
        <div className='text-muted-foreground'>Đang tải...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className='flex items-center justify-center py-8'>
        <div className='text-red-500'>Có lỗi xảy ra khi tải dữ liệu</div>
      </div>
    )
  }

  return (
    <>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Hạng thành viên</TableHead>
              <TableHead>Tích điểm</TableHead>
              <TableHead>Điều kiện đạt hạng</TableHead>
              <TableHead>Xét lại hạng theo chu kỳ</TableHead>
              <TableHead>Thời gian</TableHead>
              <TableHead>Trạng thái</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map(item => {
              const statusInfo = getStatusInfo(item?.active || 0)
              return (
                <TableRow key={item.id} className='hover:bg-muted/50 group'>
                  <TableCell>{item.type_id}</TableCell>
                  <TableCell>{item.type_name}</TableCell>
                  <TableCell>{formatPercentage(item?.point_rate || 0)}</TableCell>
                  <TableCell>{formatUpgradeCondition(item?.upgrade_amount || 0, item?.is_no_change)}</TableCell>
                  <TableCell className='whitespace-pre-line'>{formatReviewCondition(item)}</TableCell>
                  <TableCell>
                    <div className='text-xs whitespace-pre-line text-gray-600'>
                      {formatTime(item?.created_at || '', item?.updated_at || '')}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className='flex items-center'>
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${statusInfo.className}`}
                      >
                        {statusInfo.text}
                      </span>
                      <button
                        className='ml-2 text-xs text-blue-500 opacity-0 transition-opacity group-hover:opacity-100'
                        onClick={() => toggleActivation(item?.type_id || '', item?.active || 0)}
                      >
                        {item?.active === 1 ? 'Hủy' : 'Kích hoạt'}
                      </button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Pencil
                      className='h-5 w-5 cursor-pointer text-gray-400 hover:text-gray-600'
                      onClick={() => {
                        setSelectedMembershipType(item)
                        setEditDialogOpen(true)
                      }}
                    />
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>
      {data.length === 0 && <div className='text-muted-foreground py-8 text-center'>Không có dữ liệu</div>}
    </>
  )
}
