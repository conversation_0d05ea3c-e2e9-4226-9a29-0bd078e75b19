import {
  DndContext,
  closestCenter,
  DragEndEvent
} from '@dnd-kit/core'
import {
  SortableContext,
  rectSortingStrategy
} from '@dnd-kit/sortable'

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'

import { SortableMenuItem } from './sortable-menu-item'
import type { ItemsInStore } from '../data'

type ItemTypeLite = { id: string; item_type_name: string }

interface MenuItemsSortablePanelProps {
  selectedItemTypeUid: string
  itemTypes: Array<ItemTypeLite>
  sortedItems: ItemsInStore[]
  sensors: any
  onDragEnd: (event: DragEndEvent) => void
}

export function MenuItemsSortablePanel({
  selectedItemTypeUid,
  itemTypes,
  sortedItems,
  sensors,
  onDragEnd
}: MenuItemsSortablePanelProps) {
  const title = selectedItemTypeUid
    ? itemTypes.find((type) => type.id === selectedItemTypeUid)?.item_type_name || 'Uncategory'
    : 'Uncategory'

  return (
    <div className="border rounded-lg h-full flex flex-col">
      <div className="p-3 border-b bg-gray-50 flex-shrink-0">
        <h3 className="font-medium text-sm">{title}</h3>
      </div>
      <div className="flex-1 min-h-0">
        <ScrollArea className="h-[50vh] w-full">
          <div className="p-4">
            {!selectedItemTypeUid ? (
              <div className="text-center text-gray-500 py-8">
                <p>Vui lòng chọn nhóm món từ danh sách bên trái</p>
              </div>
            ) : sortedItems.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <p>Không có món ăn nào trong nhóm này</p>
              </div>
            ) : (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={onDragEnd}
              >
                <SortableContext
                  items={sortedItems.map((item) => item.id)}
                  strategy={rectSortingStrategy}
                >
                  <div className="grid grid-cols-4 gap-4">
                    {sortedItems.map((item) => (
                      <SortableMenuItem key={item.id} item={item} />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            )}
          </div>
          <ScrollBar orientation="vertical" />
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
    </div>
  )
}


