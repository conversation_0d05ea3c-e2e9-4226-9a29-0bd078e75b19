import { useStoresData } from '@/hooks/api'

import { ConfirmDialog } from '@/components/confirm-dialog'

import { useItemsInStore } from '../../../context'
import { useDeleteItemInStore, useItemsInStoreForTable } from '../../../hooks'
import { BuffetConfigModal } from './buffet-config-modal'
import { CopyMenuModal } from './copy-menu-modal'
import { ExportDialog } from './export-dialog'
import { ImportDialog } from './import-dialog'
import { PriceBySourceConfigModal } from './price-by-source-config-modal'
import { SortMenuModal } from './sort-menu-modal'
import { TimeFrameConfigModal } from './time-frame-config-modal'

export function ItemsInStoreDialogs() {
  const { open, setOpen, currentRow, setCurrentRow, selectedStoreUid } = useItemsInStore()
  const { deleteItemAsync } = useDeleteItemInStore()

  const { data: stores = [] } = useStoresData()

  const { data: menuItemsForTable = [] } = useItemsInStoreForTable({
    enabled: open === 'buffet-config' && !!currentRow
  })

  return (
    <>
      <ExportDialog open={open === 'export'} onOpenChange={() => setOpen(null)} />
      <ImportDialog open={open === 'import'} onOpenChange={() => setOpen(null)} storeUid={selectedStoreUid || currentRow?.store_uid} />
      <SortMenuModal open={open === 'sort-menu'} onOpenChange={() => setOpen(null)} />
      <CopyMenuModal open={open === 'copy-menu'} onOpenChange={() => setOpen(null)} />
      <TimeFrameConfigModal open={open === 'config-time-frame'} onOpenChange={() => setOpen(null)} />
      <PriceBySourceConfigModal
        open={open === 'config-price-by-source'}
        onOpenChange={() => setOpen(null)}
        stores={stores}
      />
      <BuffetConfigModal
        open={open === 'buffet-config'}
        onOpenChange={isOpen => {
          setOpen(null)
          if (!isOpen) {
            setTimeout(() => {
              setCurrentRow(null)
            }, 500)
          }
        }}
        itemsBuffet={[]}
        onItemsChange={() => {}}
        items={menuItemsForTable as unknown as any[]}
      />

      {currentRow && (
        <>
          <ConfirmDialog
            key='quantity-day-delete'
            destructive
            open={open === 'delete'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            handleConfirm={async () => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
              await deleteItemAsync(currentRow.id || '')
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá ?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}
    </>
  )
}
