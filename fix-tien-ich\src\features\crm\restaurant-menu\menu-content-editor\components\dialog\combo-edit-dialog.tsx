import { useEffect, useState } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import type { NormalCombo, SpecialCombo, ComboDetail } from '@/types/api/combo'
import type { MenuItem } from '@/types/api/menu-items'
import { Upload, X } from 'lucide-react'

import { EditSheet } from '@/components/ui/edit-sheet'

import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Textarea,
  Checkbox
} from '@/components/ui'

const comboFormSchema = z.object({
  combo_id: z.string().min(1, 'Mã combo là bắt buộc'),
  combo_name: z.string().min(1, 'Tên combo là bắt buộc'),
  description: z.string().optional(),
  ots_price: z.number().min(0, '<PERSON><PERSON><PERSON> tại chỗ phải lớn hơn hoặc bằng 0'),
  ta_price: z.number().min(0, '<PERSON><PERSON><PERSON> mang đi phải lớn hơn hoặc bằng 0'),
  sort: z.number().min(1, 'Thứ tự hiển thị phải lớn hơn 0'),
  allow_take_away: z.boolean(),
  ban_tai_cho: z.boolean(),
  ban_mang_di: z.boolean(),
  combo_image_path: z.string().optional()
})

type ComboFormData = z.infer<typeof comboFormSchema>

interface ComboEditDialogProps {
  item: MenuItem | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (data: ComboFormData) => void
  isLoading?: boolean
  comboType?: 'combo' | 'special-combo'
  comboData?: NormalCombo | SpecialCombo | null
}

export function ComboEditDialog({
  item,
  open,
  onOpenChange,
  onSave,
  isLoading,
  comboData = null
}: ComboEditDialogProps) {
  const [comboDetails, setComboDetails] = useState<ComboDetail[]>([])

  const form = useForm<ComboFormData>({
    resolver: zodResolver(comboFormSchema),
    defaultValues: {
      combo_id: '',
      combo_name: '',
      description: '',
      ots_price: 0,
      ta_price: 0,
      sort: 1000,
      allow_take_away: true,
      ban_tai_cho: true,
      ban_mang_di: true,
      combo_image_path: ''
    }
  })

  const handleImageUpload = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = async e => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        try {
          const imageUrl = URL.createObjectURL(file)
          form.setValue('combo_image_path', imageUrl)
        } catch (error) {}
      }
    }
    input.click()
  }

  const handleImageRemove = () => {
    form.setValue('combo_image_path', '')
  }

  useEffect(() => {
    if (item && open && comboData) {
      setComboDetails(comboData.combo_details || [])
    } else {
      setComboDetails([])
    }
  }, [item, open, comboData])

  useEffect(() => {
    if (item && open) {
      form.reset({
        combo_id: item.Item_Id,
        combo_name: item.Item_Name,
        description: item.Description || '',
        ots_price: item.Ots_Price,
        ta_price: item.Ta_Price,
        sort: item.Sort,
        allow_take_away: item.Allow_Take_Away === 1,
        ban_tai_cho: true,
        ban_mang_di: item.Allow_Take_Away === 1,
        combo_image_path: item.Item_Image_Path || ''
      })
    }
  }, [item, open, form])

  const handleSubmit = (data: ComboFormData) => {
    onSave(data)
  }

  const handleClose = () => {
    form.reset()
    onOpenChange(false)
  }

  const handleSave = () => {
    form.handleSubmit(handleSubmit)()
  }

  if (!item) return null

  return (
    <EditSheet
      open={open}
      onOpenChange={onOpenChange}
      title='CHỈNH SỬA'
      isLoading={isLoading}
      onSave={handleSave}
      onCancel={handleClose}
    >
      <div className='p-6'>
        <div className='mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4'>
          <p className='flex items-center gap-3 text-sm text-yellow-800'>
            <span className='flex h-5 w-5 items-center justify-center rounded-full bg-yellow-500 text-xs font-bold text-white'>
              !
            </span>
            Nội dung hiện thi sẽ được cập nhật trên 3 cửa hàng
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
              {/* Left Column */}
              <div className='space-y-6'>
                {/* Combo Code */}
                <FormField
                  control={form.control}
                  name='combo_id'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-semibold text-gray-700'>MÃ COMBO</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder='Nhập mã combo' className='h-10 bg-gray-100' readOnly />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Combo Name */}
                <FormField
                  control={form.control}
                  name='combo_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-semibold text-gray-700'>TÊN COMBO</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder='Nhập tên combo' className='h-10' />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description */}
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-semibold text-gray-700'>MÔN ĐẠI DIỆN</FormLabel>
                      <FormControl>
                        <Textarea {...field} placeholder='Nhập mô tả combo' className='min-h-[80px] resize-none' />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Checkboxes */}
                <div className='space-y-4'>
                  <FormField
                    control={form.control}
                    name='ban_tai_cho'
                    render={({ field }) => (
                      <FormItem className='flex flex-row items-center space-y-0 space-x-3'>
                        <FormControl>
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                        <FormLabel className='text-sm font-medium'>Bán tại chỗ</FormLabel>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='ban_mang_di'
                    render={({ field }) => (
                      <FormItem className='flex flex-row items-center space-y-0 space-x-3'>
                        <FormControl>
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                        <FormLabel className='text-sm font-medium'>Bán mang đi</FormLabel>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Right Column */}
              <div className='space-y-6'>
                {/* Image Upload */}
                <div className='space-y-4'>
                  <FormLabel className='text-sm font-semibold text-gray-700'>ẢNH MÓN</FormLabel>
                  <div className='flex h-48 w-full items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50'>
                    {form.watch('combo_image_path') ? (
                      <img
                        src={form.watch('combo_image_path')}
                        alt={form.watch('combo_name')}
                        className='h-full w-full rounded-lg object-cover'
                      />
                    ) : (
                      <div className='text-center'>
                        <Upload className='mx-auto h-12 w-12 text-gray-400' />
                        <p className='mt-2 text-sm text-gray-500'>Tải ảnh lên</p>
                      </div>
                    )}
                  </div>
                  <div className='flex gap-2'>
                    <Button type='button' variant='outline' size='sm' className='flex-1' onClick={handleImageUpload}>
                      <Upload className='mr-2 h-4 w-4' />
                      Tải ảnh lên
                    </Button>
                    <Button type='button' variant='outline' size='sm' onClick={handleImageRemove}>
                      <X className='h-4 w-4' />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Combo Details Section */}
            <div className='space-y-6'>
              <h3 className='text-lg font-semibold text-gray-800'>MÓN TRONG COMBO</h3>

              {/* Combo Items Table */}
              <div className='rounded-lg border'>
                <div className='grid grid-cols-6 gap-4 bg-gray-50 p-4 text-sm font-medium text-gray-700'>
                  <div>Tên nhóm</div>
                  <div>Giá tại chỗ</div>
                  <div>Thứ tự</div>
                  <div>Giảm giá tại chỗ</div>
                  <div>Giá mang về</div>
                  <div>Giảm giá mang về</div>
                </div>

                {/* Render combo details */}
                {comboDetails.length > 0 ? (
                  comboDetails.map((detail, index) => (
                    <div key={index} className='grid grid-cols-6 gap-4 border-t p-4 text-sm'>
                      <div>
                        <Input value={`Nhóm ${index + 1}`} className='h-8 bg-gray-100' readOnly />
                      </div>
                      <div className='flex items-center gap-2'>
                        <Input value='0' className='h-8 bg-gray-100' readOnly />
                        <span className='text-xs text-gray-500'>VNĐ</span>
                      </div>
                      <div>
                        <Input value={detail.min_permitted.toString()} className='h-8 bg-gray-100' readOnly />
                      </div>
                      <div className='flex items-center gap-2'>
                        <Input value='0' className='h-8 bg-gray-100' readOnly />
                        <span className='text-xs text-gray-500'>%</span>
                      </div>
                      <div className='flex items-center gap-2'>
                        <Input value='0' className='h-8 bg-gray-100' readOnly />
                        <span className='text-xs text-gray-500'>VNĐ</span>
                      </div>
                      <div className='flex items-center gap-2'>
                        <Input value='0' className='h-8 bg-gray-100' readOnly />
                        <span className='text-xs text-gray-500'>%</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className='border-t p-8 text-center text-gray-500'>Chưa có nhóm món nào trong combo</div>
                )}
              </div>

              {/* Combo Items List */}
              <div className='space-y-4'>
                <h4 className='text-sm font-semibold text-gray-700'>Danh sách món</h4>

                <div className='rounded-lg border'>
                  <div className='grid grid-cols-5 gap-4 bg-gray-50 p-4 text-sm font-medium text-gray-700'>
                    <div className='col-span-2'>Tên món</div>
                    <div>Giá tại chỗ</div>
                    <div>Giảm giá</div>
                    <div>Giá mang về</div>
                  </div>

                  {/* Render combo items */}
                  {comboDetails.length > 0 ? (
                    comboDetails.map((detail, detailIndex) =>
                      detail.items.map((comboItem, itemIndex) => (
                        <div
                          key={`${detailIndex}-${itemIndex}`}
                          className='grid grid-cols-5 gap-4 border-t p-4 text-sm'
                        >
                          <div className='col-span-2 font-medium'>
                            {comboItem.store_item_id} - {comboItem.name}
                          </div>
                          <div>{comboItem.ots_price || 0} VNĐ</div>
                          <div>0%</div>
                          <div>{comboItem.ta_price || 0} VNĐ</div>
                        </div>
                      ))
                    )
                  ) : (
                    <div className='border-t p-8 text-center text-gray-500'>Chưa có món nào trong combo</div>
                  )}
                </div>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </EditSheet>
  )
}
