import { UseFormReturn } from 'react-hook-form'

import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

import { CrmSettingsFormValues } from '../../data'

interface DeliveryConfigSectionProps {
  form: UseFormReturn<CrmSettingsFormValues>
  isLoading?: boolean
}

export function DeliveryConfigSection({ form, isLoading = false }: DeliveryConfigSectionProps) {
  return (
    <Card className='shadow-sm'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          CẤU HÌNH ĐẶT GIAO HÀNG
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-6 p-6'>
        {/* Nhà hàng đại diện */}
        <FormField
          control={form.control}
          name='representativeStore'
          render={({ field }) => (
            <FormItem>
              <div className='flex flex-col gap-4'>
                <FormLabel className='w-full text-sm font-medium'>Nhà hàng đại diện</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange} disabled={isLoading}>
                    <SelectTrigger className='w-96'>
                      <SelectValue placeholder='A Hưng' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='a-hung'>A Hưng</SelectItem>
                      <SelectItem value='other'>Khác</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </div>
              <div className='w-96 text-xs text-gray-500'>
                Khách hàng truy cập các trang đặt hàng sẽ thấy thực đơn của nhà hàng đại diện này. Lưu ý: Các nhà hàng
                trong thương hiệu phải có thực đơn giống nhau.
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Phí dịch vụ */}
        <FormField
          control={form.control}
          name='serviceCharge'
          render={({ field }) => (
            <FormItem>
              <div className='flex flex-col gap-4'>
                <FormLabel className='w-32 text-sm font-medium'>Phí dịch vụ</FormLabel>
                <div className='flex flex-1 items-center gap-2'>
                  <FormControl>
                    <Input
                      {...field}
                      type='number'
                      step='0.1'
                      disabled={isLoading}
                      className='w-96'
                      onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                    />
                  </FormControl>
                  <span className='text-sm'>%</span>
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* VAT */}
        <FormField
          control={form.control}
          name='vat'
          render={({ field }) => (
            <FormItem>
              <div className='flex flex-col gap-4'>
                <FormLabel className='w-32 text-sm font-medium'>VAT</FormLabel>
                <div className='flex flex-1 items-center gap-2'>
                  <FormControl>
                    <Input
                      {...field}
                      type='number'
                      step='0.1'
                      disabled={isLoading}
                      className='w-96'
                      onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                    />
                  </FormControl>
                  <span className='text-sm'>%</span>
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className='flex justify-start'>
          <Button type='button' disabled={isLoading}>
            Lưu
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
