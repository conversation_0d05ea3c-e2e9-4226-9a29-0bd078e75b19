import { useCurrentBrand } from '@/hooks/use-current-brand'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

/**
 * Component to show the current selected brand status
 * This helps verify that brand switching is working correctly
 */
export function BrandStatusIndicator() {
  const {
    selectedBrand,
    isLoading,
    brandName,
    brandCurrency,
    brandId,
    storeCount,
    activeStoreCount,
    hasActiveStores,
  } = useCurrentBrand()

  if (!selectedBrand) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Brand Status</CardTitle>
          <CardDescription>No brand selected</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Current Brand
          {isLoading && (
            <Badge variant="secondary" className="animate-pulse">
              Switching...
            </Badge>
          )}
        </CardTitle>
        <CardDescription>Selected brand information</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div>
          <div className="font-medium text-lg">{brandName}</div>
          <div className="text-sm text-muted-foreground">
            {brandCurrency} • {brandId}
          </div>
        </div>
        
        <div className="flex gap-2">
          <Badge variant="outline">
            {storeCount} {storeCount === 1 ? 'Store' : 'Stores'}
          </Badge>
          {hasActiveStores && (
            <Badge variant="default">
              {activeStoreCount} Active
            </Badge>
          )}
        </div>

        <div className="text-xs text-muted-foreground">
          Brand ID: {selectedBrand.id || 'N/A'}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Compact version for use in headers or toolbars
 */
export function BrandStatusBadge() {
  const { brandName, brandCurrency, isLoading } = useCurrentBrand()

  if (isLoading) {
    return (
      <Badge variant="secondary" className="animate-pulse">
        Switching...
      </Badge>
    )
  }

  return (
    <Badge variant="outline">
      {brandName} • {brandCurrency}
    </Badge>
  )
}
