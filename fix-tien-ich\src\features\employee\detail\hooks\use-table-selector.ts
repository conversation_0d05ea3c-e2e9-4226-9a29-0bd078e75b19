import { useState, useMemo } from 'react'

import type { Table } from '@/lib/tables-api'

import { useTablesData } from '@/hooks/api'

interface AreaWithTables {
  area_uid: string
  area_name: string
  tables: Table[]
}

interface UseTableSelectorProps {
  selectedTables: string[]
  storeId: string
  brandId: string
  enabled: boolean
}

export function useTableSelector({
  selectedTables,
  storeId,
  brandId,
  enabled
}: UseTableSelectorProps) {
  const [expandedAreas, setExpandedAreas] = useState<Set<string>>(new Set())
  const [localSelectedTables, setLocalSelectedTables] = useState<Set<string>>(
    new Set(selectedTables)
  )

  const { data: tables = [], isLoading } = useTablesData({
    storeUid: storeId,
    enabled: enabled && !!storeId && !!brandId
  })

  const areaData = useMemo((): AreaWithTables[] => {
    const areaMap = new Map<string, AreaWithTables>()

    tables.forEach(table => {
      const areaKey = table.area_uid || 'no-area'
      const areaName = table.area?.area_name || 'Không có khu vực'

      if (!areaMap.has(areaKey)) {
        areaMap.set(areaKey, {
          area_uid: areaKey,
          area_name: areaName,
          tables: []
        })
      }

      areaMap.get(areaKey)!.tables.push(table)
    })

    return Array.from(areaMap.values()).sort((a, b) => a.area_name.localeCompare(b.area_name))
  }, [tables])

  const toggleAreaExpansion = (areaUid: string) => {
    const newExpanded = new Set(expandedAreas)
    if (newExpanded.has(areaUid)) {
      newExpanded.delete(areaUid)
    } else {
      newExpanded.add(areaUid)
    }
    setExpandedAreas(newExpanded)
  }

  const handleTableToggle = (tableId: string) => {
    const newSelected = new Set(localSelectedTables)
    if (newSelected.has(tableId)) {
      newSelected.delete(tableId)
    } else {
      newSelected.add(tableId)
    }
    setLocalSelectedTables(newSelected)
  }

  const handleAreaToggle = (area: AreaWithTables) => {
    const newSelected = new Set(localSelectedTables)
    const areaTableIds = area.tables.map(t => t.id)
    const allSelected = areaTableIds.every(id => newSelected.has(id))

    if (allSelected) {
      areaTableIds.forEach(id => newSelected.delete(id))
    } else {
      areaTableIds.forEach(id => newSelected.add(id))
    }

    setLocalSelectedTables(newSelected)
  }

  const isAreaSelected = (area: AreaWithTables) => {
    const areaTableIds = area.tables.map(t => t.id)
    return areaTableIds.every(id => localSelectedTables.has(id))
  }

  const isAreaIndeterminate = (area: AreaWithTables) => {
    const areaTableIds = area.tables.map(t => t.id)
    const selectedCount = areaTableIds.filter(id => localSelectedTables.has(id)).length
    return selectedCount > 0 && selectedCount < areaTableIds.length
  }

  const resetSelection = () => {
    setLocalSelectedTables(new Set(selectedTables))
  }

  return {
    tables,
    areaData,
    isLoading,
    expandedAreas,
    toggleAreaExpansion,
    localSelectedTables,
    setLocalSelectedTables,
    resetSelection,
    handleTableToggle,
    handleAreaToggle,
    isAreaSelected,
    isAreaIndeterminate
  }
}
