import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface StatusBadgeProps {
  isActive: boolean
  activeText?: string
  inactiveText?: string
  className?: string
}

export function StatusBadge({
  isActive,
  activeText = 'Active',
  inactiveText = 'Inactive',
  className
}: StatusBadgeProps) {
  return (
    <Badge
      className={cn(
        'text-white font-medium',
        isActive
          ? 'bg-green-500 hover:bg-green-600'
          : 'bg-red-500 hover:bg-red-600',
        className
      )}
    >
      {isActive ? activeText : inactiveText}
    </Badge>
  )
}
