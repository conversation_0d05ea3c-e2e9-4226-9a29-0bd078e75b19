import type { Brand, City, Store } from '@/types/auth'

import { Button } from '@/components/ui'

import { SelectedStoreDisplay } from '../selected-store-display'
import { SelectedItemsDisplay } from '../summary/selected-items-display'

interface BrandAccessSectionProps {
  needsStoreSelection: boolean
  selectedStore: string
  selectedBrandAccess: string[]
  onShowStoreSelector: () => void
  onShowBrandSelector: () => void
  brands?: Brand[]
  cities?: City[]
  stores?: Store[]
}

export function BrandAccessSection({
  needsStoreSelection,
  selectedStore,
  selectedBrandAccess,
  onShowStoreSelector,
  onShowBrandSelector,
  brands,
  cities,
  stores
}: BrandAccessSectionProps) {
  return (
    <div className='space-y-6'>
      <h3 className='text-base font-medium'>Cho phép truy cập vào thương hiệu</h3>

      <div className='space-y-1'>
        {needsStoreSelection ? (
          <SelectedStoreDisplay selectedStore={selectedStore} brands={brands} cities={cities} stores={stores} />
        ) : (
          <SelectedItemsDisplay selectedItems={selectedBrandAccess} />
        )}

        <Button
          type='button'
          variant='outline'
          onClick={needsStoreSelection ? onShowStoreSelector : onShowBrandSelector}
          className='w-full justify-center text-blue-600'
        >
          {needsStoreSelection ? 'Chọn cửa hàng' : 'Chọn thương hiệu và cửa hàng'}
        </Button>
      </div>
    </div>
  )
}
