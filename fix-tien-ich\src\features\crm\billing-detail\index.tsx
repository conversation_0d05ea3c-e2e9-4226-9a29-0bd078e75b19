import { useSearch } from '@tanstack/react-router'

import { useBillingDetail } from '@/hooks/crm'

import { Main } from '@/components/layout/main'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui'

import { BillingDetailTable } from './components'

interface BillingDetailSearch {
  date: string
  type: string
}

export default function BillingDetailPage() {
  const { date, type } = useSearch({ from: '/_authenticated/crm/billing-detail' }) as BillingDetailSearch

  const apiParams = {
    page: 1,
    type: type,
    date_start: date,
    date_end: date,
    pos_parent: 'BRAND-953H'
  }

  const billingDetailQuery = useBillingDetail(apiParams)

  return (
    <Main>
      <div className='container mx-auto space-y-6 py-6'>
        <div className='flex items-center justify-between'>
          <h1 className='text-3xl font-bold tracking-tight'>Chi tiết lưu lượng sử dụng</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Kết quả: {billingDetailQuery.data?.count || 0} Bản ghi</CardTitle>
          </CardHeader>
          <CardContent>
            <BillingDetailTable
              data={billingDetailQuery.data?.vouchers || []}
              isLoading={billingDetailQuery.isLoading}
            />
          </CardContent>
        </Card>
      </div>
    </Main>
  )
}
