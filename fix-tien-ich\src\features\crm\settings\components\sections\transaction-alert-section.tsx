import { UseFormReturn } from 'react-hook-form'

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem
} from '@/components/ui'

import { CrmSettingsFormValues } from '../../data'
import { useSettingsApi } from '../../hooks/use-settings-api'
import { EmailTagsInput } from '../email-tags-input'

interface TransactionAlertSectionProps {
  form: UseFormReturn<CrmSettingsFormValues>
  isLoading?: boolean
}

export function TransactionAlertSection({ form, isLoading = false }: TransactionAlertSectionProps) {
  const transactionEmailType = form.watch('transactionEmailType')
  const { saveTransactionAlert, isLoading: isSaving } = useSettingsApi()

  const handleSave = async () => {
    const values = form.getValues()
    await saveTransactionAlert({
      enableAccountBalanceAlert: values.enableAccountBalanceAlert,
      balanceThreshold: values.balanceThreshold,
      enableAccountBalanceAlertVND: values.enableAccountBalanceAlertVND,
      balanceThresholdVND: values.balanceThresholdVND,
      transactionEmailType: values.transactionEmailType,
      transactionCustomEmails: values.transactionCustomEmails
    })
  }
  return (
    <Card className='shadow-sm'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          CẢNH BÁO GIAO DỊCH BẤT THƯỜNG
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-6 p-6'>
        {/* Thông báo danh sách tài khoản chi tiêu vượt */}
        <div className='space-y-4'>
          <div className='flex flex-col gap-4'>
            <FormLabel className='w-full text-sm font-medium'>Thông báo danh sách tài khoản chi tiêu vượt</FormLabel>
            <div className='flex flex-1 items-center gap-2'>
              <FormField
                control={form.control}
                name='balanceThreshold'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        {...field}
                        type='number'
                        disabled={isLoading}
                        className='w-24'
                        onChange={e => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button variant='outline' size='sm' disabled={isLoading}>
                Lần một ngày
              </Button>
            </div>
          </div>

          <div className='flex flex-col gap-4'>
            <div className='flex flex-1 items-center gap-2'>
              <FormField
                control={form.control}
                name='balanceThresholdVND'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        {...field}
                        type='number'
                        disabled={isLoading}
                        className='w-24'
                        onChange={e => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button variant='outline' size='sm' disabled={isLoading}>
                VNĐ một ngày
              </Button>
            </div>
          </div>
        </div>

        {/* Email nhận thông báo */}
        <FormField
          control={form.control}
          name='transactionEmailType'
          render={({ field }) => (
            <FormItem>
              <div className='flex w-full flex-col gap-4'>
                <FormLabel className='w-full text-sm font-medium'>Email nhận thông báo</FormLabel>
                <FormControl>
                  <RadioGroup
                    value={field.value}
                    onValueChange={field.onChange}
                    className='flex gap-6'
                    disabled={isLoading}
                  >
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='regular' id='transaction-regular' />
                      <label htmlFor='transaction-regular' className='text-sm'>
                        Email thường hiệu
                      </label>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='other' id='transaction-other' />
                      <label htmlFor='transaction-other' className='text-sm'>
                        Email khác
                      </label>
                    </div>
                  </RadioGroup>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Conditional Email Input for Custom Emails */}
        {transactionEmailType === 'other' && (
          <FormField
            control={form.control}
            name='transactionCustomEmails'
            render={({ field }) => (
              <FormItem>
                <div className='flex flex-col gap-4'>
                  <FormControl>
                    <EmailTagsInput
                      value={field.value || []}
                      onChange={field.onChange}
                      disabled={isLoading}
                      className='w-96'
                      placeholder='Nhập email và ấn Enter'
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <div className='flex justify-start'>
          <Button type='button' disabled={isLoading || isSaving} onClick={handleSave}>
            {isSaving ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
