import { useState, useRef } from 'react'

import { ItemCategory } from '@/types/item-categories'
import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import {
  useItemCategoriesData,
  useDeleteItemCategory,
  useExportItemCategories,
  useUpdateItemCategoryStatus,
  useBulkCreateItemCategories
} from '@/hooks/api'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'

import { ConfirmModal, PosModal } from '@/components/pos'
import { Button, Input } from '@/components/ui'

import { categoryColumns, CategoryDataTable } from './components'
import { generateCategoryId } from '@/lib/id-generators'

export default function CategoriesInBrandPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [importModalOpen, setImportModalOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [parsedData, setParsedData] = useState<Array<{ id: string; name: string }>>([])
  const [showParsedData, setShowParsedData] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [selectedCategory, setSelectedCategory] = useState<ItemCategory | null>(null)
  const [modalAction, setModalAction] = useState<'activate' | 'deactivate' | 'delete' | null>(null)

  const {
    data: itemCategories,
    isLoading,
    error
  } = useItemCategoriesData({
    searchTerm: searchTerm || undefined
  })

  const deleteCategoryMutation = useDeleteItemCategory()
  const exportCategoriesMutation = useExportItemCategories()
  const updateCategoryStatusMutation = useUpdateItemCategoryStatus()
  const bulkCreateCategoriesMutation = useBulkCreateItemCategories()

  const handleDeleteCategory = (category: ItemCategory) => {
    setSelectedCategory(category)
    setModalAction('delete')
    setConfirmModalOpen(true)
  }

  const handleImportCategories = () => {
    setImportModalOpen(true)
    setShowParsedData(false)
    setParsedData([])
    setSelectedFile(null)
  }

  const handleFileUpload = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      await parseExcelFile(file)
    }
  }

  const parseExcelFile = async (file: File) => {
    try {
      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

      // Parse data according to format: Row 1: name, id; Row 2+: data
      const parsedCategories: Array<{ id: string; name: string }> = []

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i] as unknown[]
        if (row && row[0]) { // Only require name, id is optional
          parsedCategories.push({
            name: String(row[0]).trim(),
            id: row[1] ? String(row[1]).trim() : generateCategoryId() // Generate ID if not provided
          })
        }
      }

      setParsedData(parsedCategories)
      setShowParsedData(true)
      toast.success(`Đã phân tích ${parsedCategories.length} nhóm món từ file!`)
    } catch {
      toast.error('Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.')
    }
  }

  const handleSaveImportedData = async () => {
    if (parsedData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return
    }

    try {
      const categories = parsedData.map((item, index) => ({
        item_type_name: item.name,
        item_type_id: item.id,
        sort: index + 1
      }))

      await bulkCreateCategoriesMutation.mutateAsync(categories)
      toast.success(`Đã tạo thành công ${categories.length} nhóm món!`)
      setImportModalOpen(false)
      setShowParsedData(false)
      setParsedData([])
      setSelectedFile(null)
    } catch {
      toast.error('Lỗi khi tạo nhóm món. Vui lòng thử lại.')
    }
  }

  const handleDownloadTemplate = () => {
    const link = document.createElement('a')
    link.href = '/files/categories/categories-in-brand/import_item_type_template.xlsx'
    link.download = 'import_item_type_template.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleCloseImportModal = () => {
    setImportModalOpen(false)
    setShowParsedData(false)
    setParsedData([])
    setSelectedFile(null)
  }

  const handleToggleCategoryStatus = (category: ItemCategory) => {
    setSelectedCategory(category)
    setModalAction(category.active === 1 ? 'deactivate' : 'activate')
    setConfirmModalOpen(true)
  }

  const handleConfirmAction = async () => {
    if (!selectedCategory) return

    try {
      if (modalAction === 'delete') {
        await deleteCategoryMutation.mutateAsync(selectedCategory.id)
        toast.success(`Nhóm "${selectedCategory.item_type_name}" đã được xóa thành công!`)
      } else {
        const updatedCategory = {
          ...selectedCategory,
          active: selectedCategory.active === 1 ? 0 : 1
        }

        await updateCategoryStatusMutation.mutateAsync(updatedCategory)

        const action = selectedCategory.active === 1 ? 'vô hiệu hóa' : 'kích hoạt'
        toast.success(`Đã ${action} nhóm "${selectedCategory.item_type_name}" thành công!`)
      }

      setConfirmModalOpen(false)
      setSelectedCategory(null)
      setModalAction(null)
    } catch {
      if (modalAction === 'delete') {
        toast.error('Không thể xóa nhóm món')
      } else {
        toast.error('Có lỗi xảy ra khi cập nhật trạng thái nhóm món')
      }
    }
  }

  const handleExportCategories = async () => {
    try {
      const blob = await exportCategoriesMutation.mutateAsync({})
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `categories-export-${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success('Nhóm món đã được xuất thành công!')
    } catch {
      toast.error('Không thể xuất nhóm món')
    }
  }

  const getModalConfig = () => {
    if (modalAction === 'delete') {
      return {
        title: 'Xác nhận xóa nhóm món',
        content:
          'Bạn có chắc muốn xóa nhóm món đã chọn?\nCác món trong nhóm này sẽ đưa về Uncategory',
        confirmText: 'Xác nhận'
      }
    } else if (modalAction === 'deactivate') {
      return {
        title: 'Xác nhận deactivate nhóm món',
        content:
          'Bạn có chắc muốn deactivate nhóm món đã chọn? Các món trong nhóm này sẽ bị deactivate',
        confirmText: 'Xác nhận'
      }
    } else {
      return {
        title: 'Xác nhận activate nhóm món',
        content: 'Khi Active lại toàn bộ món trong nhóm sẽ được Active',
        confirmText: 'Xác nhận'
      }
    }
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-6 flex items-center justify-between'>
        <div className='flex flex-col'>
          <h2 className='text-xl font-semibold'>Nhóm món toàn thương hiệu</h2>
          <p className='mt-1 text-sm text-gray-600'>
            Nhóm món giúp bạn sắp xếp và tổ chức các món, báo cáo về doanh số bán hàng và định tuyến
            các món đến máy in cụ thể.
          </p>
        </div>
        <div className='flex items-center gap-4'>
          <Input
            placeholder='Tìm kiếm nhóm món'
            className='w-64'
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault()
                setSearchTerm(searchQuery)
              }
            }}
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size='sm' variant='outline'>
                Tiện ích
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onClick={handleImportCategories}>
                Thêm nhóm từ file
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExportCategories}>Xuất nhóm món</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button size='sm' variant='default'>
            Tạo nhóm
          </Button>
        </div>
      </div>

      {error ? (
        <div className='py-8 text-center'>
          <p className='text-red-600'>Có lỗi xảy ra khi tải nhóm món</p>
        </div>
      ) : isLoading ? (
        <div className='py-8 text-center'>
          <p>Đang tải dữ liệu nhóm món...</p>
        </div>
      ) : (
        <CategoryDataTable
          columns={categoryColumns}
          data={itemCategories || []}
          onDeleteCategory={handleDeleteCategory}
          onToggleCategoryStatus={handleToggleCategoryStatus}
        />
      )}

      <ConfirmModal
        open={confirmModalOpen}
        onOpenChange={setConfirmModalOpen}
        title={getModalConfig().title}
        content={getModalConfig().content}
        confirmText={getModalConfig().confirmText}
        cancelText='Hủy'
        onConfirm={handleConfirmAction}
        isLoading={
          modalAction === 'delete'
            ? deleteCategoryMutation.isPending
            : updateCategoryStatusMutation.isPending
        }
      />

      <PosModal
        title='Thêm nhóm'
        open={importModalOpen}
        onOpenChange={setImportModalOpen}
        onCancel={handleCloseImportModal}
        onConfirm={showParsedData ? handleSaveImportedData : handleFileUpload}
        confirmText={showParsedData ? 'Lưu' : 'Tải file lên'}
        isLoading={bulkCreateCategoriesMutation.isPending}
        hideButtons={!showParsedData}
        centerTitle={true}
      >
        <div className='space-y-4'>
          {!showParsedData ? (
            <>
              <p className='text-sm text-gray-600'>File tải lên có cấu trúc như sau:</p>
              <img
                src='/images/categories/categories-in-brand/template-tao-nhom-mon.png'
                alt='Template tạo nhóm món'
                className='mx-auto max-w-full rounded-md border'
              />
              <p className='text-sm text-gray-600'>
                Hoặc xem trong{' '}
                <span
                  className='cursor-pointer font-medium text-blue-600 underline hover:text-blue-800'
                  onClick={handleDownloadTemplate}
                >
                  file mẫu
                </span>
              </p>
              <div className='flex justify-center'>
                <Button onClick={handleFileUpload} variant='outline' className='w-full'>
                  Tải file lên
                </Button>
              </div>
              <input
                ref={fileInputRef}
                type='file'
                accept='.xlsx,.xls'
                onChange={handleFileChange}
                className='hidden'
              />
              {selectedFile && (
                <p className='text-center text-sm text-gray-600'>
                  File đã chọn: {selectedFile.name}
                </p>
              )}
            </>
          ) : (
            <>
              <div className='max-h-60 overflow-y-auto rounded-md border'>
                <table className='w-full text-sm'>
                  <thead className='bg-gray-50'>
                    <tr>
                      <th className='border-b px-3 py-2 text-left'>ID</th>
                      <th className='border-b px-3 py-2 text-left'>Name</th>
                    </tr>
                  </thead>
                  <tbody>
                    {parsedData.map((item, index) => (
                      <tr key={index} className='border-b'>
                        <td className='px-3 py-2'>{item.id}</td>
                        <td className='px-3 py-2'>{item.name}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </div>
      </PosModal>
    </div>
  )
}
