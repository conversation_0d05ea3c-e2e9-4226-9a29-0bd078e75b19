import { useMemo } from 'react'

import type { User } from '@/types/user'
import { toast } from 'sonner'

import { useExcelExport } from '@/hooks/use-excel-export'

interface UseEmployeeExportProps {
  employees: User[]
  stores?: Array<{ id: string; store_name: string }>
}

export function useEmployeeExport({ employees, stores }: UseEmployeeExportProps) {
  const exportData = useMemo(() => {
    return employees.map(employee => {
      // Parse the nested stores structure: { brandId: { cityId: [storeId1, storeId2] } }
      const userStores = employee.stores || {}
      const storeIds: string[] = []

      // Extract all store IDs from the nested structure
      Object.values(userStores).forEach(brandStores => {
        if (typeof brandStores === 'object' && brandStores !== null) {
          Object.values(brandStores).forEach(cityStores => {
            if (Array.isArray(cityStores)) {
              storeIds.push(...(cityStores as string[]))
            }
          })
        }
      })

      // Map store IDs to store names
      const storeNames = storeIds
        .map(storeId => {
          const store = stores?.find(s => s.id === storeId)
          return store?.store_name || `Store ${storeId.slice(0, 8)}...`
        })
        .filter(Boolean)

      return {
        id: employee.id,
        full_name: employee.full_name,
        role_name: employee.role_name,
        email: employee.email,
        phone: employee.phone || '',
        status: employee.active === 1 ? 'Active' : 'Inactive',
        stores: storeNames.length > 0 ? storeNames.join(', ') : ''
      }
    })
  }, [employees, stores])

  const { exportData: exportToExcel, isExporting } = useExcelExport({
    data: exportData,
    filename: `danh-sach-nhan-vien-${new Date().toISOString().split('T')[0]}.xlsx`,
    sheetName: 'Danh sách nhân viên',
    columnMapping: {
      id: 'ID',
      full_name: 'Tên',
      role_name: 'Chức vụ',
      email: 'Email',
      phone: 'Số điện thoại',
      status: 'Trạng thái',
      stores: 'Cửa hàng'
    },
    onExportStart: () => {
      toast.info('Đang xuất danh sách nhân viên...')
    },
    onExportComplete: () => {
      toast.success('Xuất danh sách nhân viên thành công!')
    },
    onExportError: error => {
      console.error('Export error:', error)
      toast.error('Có lỗi xảy ra khi xuất danh sách nhân viên')
    }
  })

  const handleExportEmployees = () => {
    if (employees.length === 0) {
      toast.warning('Không có dữ liệu nhân viên để xuất')
      return
    }
    exportToExcel()
  }

  return {
    handleExportEmployees,
    isExporting
  }
}
