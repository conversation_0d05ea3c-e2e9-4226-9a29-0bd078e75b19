import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

// import { useToast } from '@/hooks/use-toast'

import { crmSettingsSchema, defaultCrmSettingsValues, type CrmSettingsFormValues } from '../data'

export function useCrmSettingsForm() {
  // const { toast } = useToast()

  const form = useForm<CrmSettingsFormValues>({
    resolver: zodResolver(crmSettingsSchema),
    defaultValues: defaultCrmSettingsValues
  })

  const handleSubmit = async (values: CrmSettingsFormValues) => {
    try {
      // TODO: Implement API call to save settings
      console.log('Saving CRM settings:', values)

      // toast({
      //   title: 'Thành công',
      //   description: 'Cài đặt đã được lưu thành công'
      // })
    } catch (error) {
      console.error('Error saving CRM settings:', error)
      // toast({
      //   title: 'Lỗi',
      //   description: '<PERSON>ó lỗi xảy ra khi lưu cài đặt',
      //   variant: 'destructive'
      // })
    }
  }

  return {
    form,
    handleSubmit,
    isLoading: false // TODO: Add loading state when API is implemented
  }
}
