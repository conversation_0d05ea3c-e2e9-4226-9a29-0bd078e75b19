import type { Brand, City, Store } from '@/types/auth'

import { useAuthStore } from '@/stores/authStore'

interface SelectedStoreDisplayProps {
  selectedStore: string
  brands?: Brand[]
  cities?: City[]
  stores?: Store[]
}

export function SelectedStoreDisplay({
  selectedStore,
  brands: propBrands,
  cities: propCities,
  stores: propStores
}: SelectedStoreDisplayProps) {
  const { brands: authBrands, cities: authCities, stores: authStores } = useAuthStore(state => state.auth)

  const brands = propBrands || authBrands
  const cities = propCities || authCities
  const stores = propStores || authStores

  const storeInfo = () => {
    if (!selectedStore || !brands || !cities || !stores) return null

    const storeId = selectedStore.replace('store:', '')

    const store = stores.find(s => s.id === storeId)
    if (!store) return null

    const city = cities.find(c => c.id === store.city_uid)
    const brand = brands.find(b => b.id === store.brand_uid)

    return {
      storeName: store.store_name,
      cityName: city?.city_name || '',
      brandName: brand?.brand_name || ''
    }
  }

  const storeData = storeInfo()

  if (!storeData) {
    return null
  }

  return (
    <div className='space-y-3'>
      <div className='rounded-md border bg-gray-50'>
        <div className='grid grid-cols-2 border-b bg-white last:border-b-0 hover:bg-gray-50'>
          <div className='p-3 text-sm font-medium text-gray-700'>{storeData.brandName}</div>
          <div className='p-3 text-sm text-gray-600'>
            {storeData.cityName} - {storeData.storeName}
          </div>
        </div>
      </div>
    </div>
  )
}
