import { useState } from 'react'

import { IconFileExport, IconFilter } from '@tabler/icons-react'

import { useAuthStore } from '@/stores/authStore'

import { useRolesData } from '@/hooks/api'

import { FilterDropdown } from '@/components/filter-dropdown'
import { Button, Input } from '@/components/ui'

interface EmployeesHeaderProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  onSearchSubmit?: () => void
  selectedRole: string
  onRoleChange: (value: string) => void
  selectedStatus: string
  onStatusChange: (value: string) => void
  selectedBrand?: string
  onBrandChange?: (value: string) => void
  selectedCity?: string
  onCityChange?: (value: string) => void
  selectedStore?: string
  onStoreChange?: (value: string) => void

  onExportEmployees: () => void
  onInviteEmployee: () => void
  isExporting?: boolean
}

export function EmployeesHeader({
  searchTerm,
  onSearchChange,
  onSearchSubmit,
  selectedRole,
  onRoleChange,
  selectedStatus,
  onStatusChange,
  selectedBrand = 'all',
  onBrandChange,
  selectedCity = 'all',
  onCityChange,
  selectedStore = 'all',
  onStoreChange,
  onExportEmployees,
  onInviteEmployee,
  isExporting = false
}: EmployeesHeaderProps) {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  const { brands, cities, stores } = useAuthStore(state => state.auth)

  const { data: roles = [] } = useRolesData()

  const roleOptions = roles.map(role => ({
    value: role.role_id,
    label: role.role_name
  }))

  const statusOptions = [
    { value: 'active', label: 'Tài khoản hoạt động' },
    { value: 'inactive', label: 'Tài khoản ngừng hoạt động' }
  ]

  const brandOptions =
    brands
      ?.filter(brand => brand.active === 1)
      .map(brand => ({
        value: brand.id,
        label: brand.brand_name
      })) || []

  const getCityOptions = () => {
    if (selectedBrand === 'all') return []

    const brandStores = stores?.filter(store => store.active === 1 && store.brand_uid === selectedBrand) || []

    const uniqueCityIds = [...new Set(brandStores.map(store => store.city_uid))]

    return (
      cities
        ?.filter(city => city.active === 1 && uniqueCityIds.includes(city.id))
        .map(city => ({
          value: city.id,
          label: city.city_name
        })) || []
    )
  }

  // Store options based on selected city
  const getStoreOptions = () => {
    if (selectedCity === 'all') return []

    // Filter stores that belong to the selected city and brand
    return (
      stores
        ?.filter(
          store =>
            store.active === 1 &&
            store.city_uid === selectedCity &&
            (selectedBrand === 'all' || store.brand_uid === selectedBrand)
        )
        .map(store => ({
          value: store.id,
          label: store.store_name
        })) || []
    )
  }

  // Handle cascading changes
  const handleBrandChange = (value: string) => {
    onBrandChange?.(value)
    onCityChange?.('all')
    onStoreChange?.('all')
  }

  const handleCityChange = (value: string) => {
    onCityChange?.(value)
    onStoreChange?.('all')
  }

  const handleStoreChange = (value: string) => {
    onStoreChange?.(value)
  }

  return (
    <div className='mb-6 space-y-4'>
      {/* Title */}
      <h2 className='text-xl font-semibold'>Danh sách nhân viên</h2>

      {/* Filters and Actions */}
      <div className='flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
        {/* Search and Filters */}
        <div className='flex flex-1 items-center gap-4'>
          <Input
            placeholder='Tìm kiếm email, SĐT...'
            value={searchTerm}
            onChange={e => onSearchChange(e.target.value)}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault()
                onSearchSubmit?.()
              }
            }}
            className='max-w-sm'
          />

          <FilterDropdown
            value={selectedRole}
            onValueChange={onRoleChange}
            options={roleOptions}
            placeholder='Tất cả chức vụ'
            allOptionLabel='Tất cả chức vụ'
            className='w-[180px]'
          />

          <Button
            variant='outline'
            size='sm'
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className='flex items-center gap-2'
          >
            <IconFilter className='h-4 w-4' />
            Nâng cao
          </Button>
        </div>

        {/* Action Buttons */}
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='sm' onClick={onExportEmployees} disabled={isExporting}>
            <IconFileExport className='h-4 w-4' />
            {isExporting ? 'Đang xuất...' : 'Xuất DS nhân viên'}
          </Button>

          <Button size='sm' onClick={onInviteEmployee}>
            Tạo nhân viên
          </Button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4'>
          {/* Status Filter */}
          <FilterDropdown
            value={selectedStatus}
            onValueChange={onStatusChange}
            options={statusOptions}
            placeholder='Tất cả tài khoản'
            allOptionLabel='Tất cả tài khoản'
            className='w-full'
          />

          {/* Brand Filter */}
          <FilterDropdown
            value={selectedBrand}
            onValueChange={handleBrandChange}
            options={brandOptions}
            placeholder='Tất cả thương hiệu'
            allOptionLabel='Tất cả thương hiệu'
            className='w-full'
          />

          {/* City Filter - disabled until brand is selected */}
          <FilterDropdown
            value={selectedCity}
            onValueChange={selectedBrand === 'all' ? () => {} : handleCityChange}
            options={selectedBrand === 'all' ? [] : getCityOptions()}
            placeholder={selectedBrand === 'all' ? 'Chọn thương hiệu trước' : 'Chọn thành phố'}
            allOptionLabel='Tất cả thành phố'
            className={`w-full ${selectedBrand === 'all' ? 'cursor-not-allowed opacity-50' : ''}`}
          />

          {/* Store Filter - disabled until city is selected */}
          <FilterDropdown
            value={selectedStore}
            onValueChange={selectedCity === 'all' ? () => {} : handleStoreChange}
            options={selectedCity === 'all' ? [] : getStoreOptions()}
            placeholder={selectedCity === 'all' ? 'Chọn thành phố trước' : 'Chọn cửa hàng'}
            allOptionLabel='Tất cả cửa hàng'
            className={`w-full ${selectedCity === 'all' ? 'cursor-not-allowed opacity-50' : ''}`}
          />
        </div>
      )}
    </div>
  )
}
