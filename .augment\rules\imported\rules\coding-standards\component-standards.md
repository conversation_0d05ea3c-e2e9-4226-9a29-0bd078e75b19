---
type: "always_apply"
---

# Component Standards

## Component Structure
- **Initialize hooks** at the top of components
- **Extract complex logic** into custom hooks
- **Create handler functions** that combine hook actions
- **Keep JSX clean** and focused on rendering
- **Use proper TypeScript** for all props and state

## Code Splitting Patterns
- **Lazy load feature components** using `React.lazy()` and `Suspense`
- **Route-based splitting** - Split at page/route level first
- **Feature-based splitting** - Large features should be separate chunks
- **Component-based splitting** - Heavy components (charts, editors) should be lazy loaded
- **Use dynamic imports** for conditional feature loading
- **Implement proper loading states** with Suspense fallbacks
- **Preload critical routes** using `<link rel="prefetch">` or router preloading

## State Management Rules
- **Use custom hooks** for complex state logic
- **Zustand stores** for global application state
- **React Query** for server state management
- **Local useState** only for simple UI state
