export interface DeviceFormData {
  lastUpdate: string
  deviceName: string
  deviceCode: string
  deviceType: string
  storeLocation: string
  localIpAddress: string
  version: string
  timezone: string
  isActive: boolean
  enableTabManagement: boolean
  displayColumns: string
  enableScreen2: boolean
  useItemInStore: boolean
  kdsNotificationConfig: string
  enablePosNutMode: boolean
  typeAllowConnectPos: string
  enableComboGroup: boolean
  enableTabDisplay: boolean
  deviceTypeLocal: string
  newIpAddress: string
  specialConfigType: string
  extra_data?: {
    item_type_ignore?: string[]
    [key: string]: any
  }
}

export interface Store {
  id: string
  name: string
}
