export const DEVICE_TYPE_OPTIONS = [
  { value: 'POS', label: 'P<PERSON>', apiValue: 'P<PERSON>' },
  { value: 'POS_MINI', label: 'POS MINI', apiValue: 'POS_MINI' },
  { value: 'PDA', label: '<PERSON><PERSON>', apiValue: 'PDA' },
  { value: 'KDS', label: 'KDS', apiValue: 'KDS' },
  { value: 'KDS_ORDER_CONTROL', label: 'KDS ORDER CONTROL', apiValue: 'KDS_ORDER_CONTROL' },
  { value: 'KDS_MAKER', label: 'KDS MAKER', apiValue: 'KDS_MAKER' }
]

export const DEVICE_STATUS_OPTIONS = [
  { value: 'Hoạt động', label: 'Hoạt động', apiValue: 1 },
  { value: 'Không hoạt động', label: 'Không hoạt động', apiValue: 0 }
]

export const DEVICE_TYPE_LOCAL_OPTIONS = [
  { value: 'None', label: 'None', apiValue: 0 },
  { value: '<PERSON><PERSON>y chủ', label: '<PERSON><PERSON><PERSON> chủ', apiValue: 1 },
  { value: 'Máy trạm', label: 'Máy trạm', apiValue: 2 }
]

export const TYPE_ALLOW_CONNECT_POS_OPTIONS = [
  { value: 'Cho phép', label: 'Cho phép', apiValue: 1 },
  { value: 'Không cho phép', label: 'Không cho phép', apiValue: 0 }
]

export const KDS_NOTIFICATION_OPTIONS = [
  { value: 'Không hiển thị', label: 'Không hiển thị', apiValue: 0 },
  {
    value: 'Nhận thông báo từ KDS với order từ thiết bị',
    label: 'Nhận thông báo từ KDS với order từ thiết bị',
    apiValue: 1
  },
  { value: 'Nhận tất cả thông báo từ KDS', label: 'Nhận tất cả thông báo từ KDS', apiValue: 2 }
]

export const DEFAULT_FORM_DATA = {
  lastUpdate: '',
  deviceName: '',
  deviceCode: '',
  deviceType: 'POS',
  storeLocation: '',
  localIpAddress: '',
  version: '',
  timezone: 'Asia/Ho_Chi_Minh',
  isActive: true,
  enableTabManagement: false,
  displayColumns: '5',
  enableScreen2: false,
  useItemInStore: false,
  kdsNotificationConfig: 'Không hiển thị',
  enablePosNutMode: false,
  typeAllowConnectPos: 'Cho phép',
  enableComboGroup: false,
  enableTabDisplay: true,
  deviceTypeLocal: 'None',
  newIpAddress: '',
  specialConfigType: ''
}
