import { IconUpload } from '@tabler/icons-react'

import { ParsedCategoryData } from '@/types/categories'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

import { PosModal } from '@/components/pos'
import { Button } from '@/components/ui'

interface ImportCategoriesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  showImportParsedData: boolean
  importSelectedFile: File | null
  importParsedData: ParsedCategoryData[]
  isLoading: boolean
  onCancel: () => void
  onConfirm?: () => void
  onDownloadTemplate: () => void
  onImportFileUpload: () => void
}

export function ImportCategoriesModal({
  open,
  onOpenChange,
  showImportParsedData,
  importSelectedFile: _importSelectedFile,
  importParsedData,
  isLoading,
  onCancel,
  onConfirm,
  onDownloadTemplate,
  onImportFileUpload
}: ImportCategoriesModalProps) {
  return (
    <PosModal
      title='Thêm nhóm tại cửa hàng'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm ? onConfirm : () => {}}
      confirmText={showImportParsedData ? 'Lưu' : undefined}
      cancelText={showImportParsedData ? 'Hủy' : undefined}
      hideButtons={!showImportParsedData}
      centerTitle={true}
      maxWidth={showImportParsedData ? 'sm:max-w-4xl' : 'sm:max-w-[500px]'}
      isLoading={isLoading}
    >
      <div className='space-y-4'>
        {!showImportParsedData ? (
          <>
            {/* Step 1: Instructions */}
            <div className='space-y-4'>
              <p className='text-sm text-gray-600'>File tải lên có cấu trúc như sau:</p>

              {/* Template Image */}
              <div className='flex justify-center'>
                <img
                  src='/images/categories/categories-in-store/template-tao-nhom-mon.png'
                  alt='Template structure'
                  className='h-auto max-w-full rounded-md border'
                />
              </div>

              {/* Template Download Link - Left aligned */}
              <div className='text-left'>
                <span className='text-sm text-gray-600'>
                  Hoặc xem trong{' '}
                  <button
                    onClick={onDownloadTemplate}
                    className='text-blue-600 underline hover:text-blue-800'
                  >
                    file mẫu
                  </button>
                </span>
              </div>

              {/* Single Upload Button - Center */}
              <div className='flex justify-center pt-4'>
                <Button onClick={onImportFileUpload} className='flex items-center gap-2'>
                  <IconUpload className='h-4 w-4' />
                  Tải file excel lên
                </Button>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Step 2: Data Preview */}
            <div className='space-y-4'>
              <div className='max-h-96 overflow-auto rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Tên nhóm</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {importParsedData.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.id}</TableCell>
                        <TableCell>{item.name}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </>
        )}
      </div>
    </PosModal>
  )
}
