import { ColumnDef } from '@tanstack/react-table'

import type { MenuItemsDataType } from '../types'
import { createDanhSachNhomColumns } from '../item-type/columns'
import { createDanhSachMonColumns } from '../items/columns'
import { createDanhSachComboColumns } from '../combo/columns'
import { createDanhSachComboTuyChinhColumns } from '../combo-special/columns'

export const createMenuItemsColumns = (type: MenuItemsDataType): ColumnDef<any>[] => {
  switch (type) {
    case 'item-type':
      return createDanhSachNhomColumns()
    case 'items':
      return createDanhSachMonColumns()
    case 'combo':
      return createDanhSachComboColumns()
    case 'combo-special':
      return createDanhSachComboTuyChinhColumns()
    default:
      return createDanhSachMonColumns()
  }
}


