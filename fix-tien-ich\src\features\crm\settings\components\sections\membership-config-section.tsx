import { UseFormReturn } from 'react-hook-form'

import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

import { CrmSettingsFormValues } from '../../data'

interface MembershipConfigSectionProps {
  form: UseFormReturn<CrmSettingsFormValues>
  isLoading?: boolean
}

export function MembershipConfigSection({ form, isLoading = false }: MembershipConfigSectionProps) {
  return (
    <Card className='shadow-sm'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          CHƯƠNG TRÌNH THÀNH VIÊN
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-6 p-6'>
        {/* Làm tròn điểm tích lũy */}
        <FormField
          control={form.control}
          name='enablePointAccumulation'
          render={({ field }) => (
            <FormItem>
              <div className='flex flex-col gap-4'>
                <FormLabel className='w-full text-sm font-medium'>Làm tròn điểm tích lũy</FormLabel>
                <FormControl>
                  <RadioGroup
                    value={field.value ? 'yes' : 'no'}
                    onValueChange={value => field.onChange(value === 'yes')}
                    className='flex gap-6'
                    disabled={isLoading}
                  >
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='yes' id='point-yes' />
                      <label htmlFor='point-yes' className='text-sm'>
                        Có
                      </label>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='no' id='point-no' />
                      <label htmlFor='point-no' className='text-sm'>
                        Không
                      </label>
                    </div>
                  </RadioGroup>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Chu kỳ xét duyệt thay đổi hạng thành viên tự động */}
        <FormField
          control={form.control}
          name='enableAutoMemberUpgrade'
          render={({ field }) => (
            <FormItem>
              <div className='flex flex-col gap-4'>
                <FormLabel className='w-full text-sm font-medium'>
                  Chu kỳ xét duyệt thay đổi hạng thành viên tự động
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    value={field.value ? 'yes' : 'no'}
                    onValueChange={value => field.onChange(value === 'yes')}
                    className='flex gap-6'
                    disabled={isLoading}
                  >
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='no' id='upgrade-no' />
                      <label htmlFor='upgrade-no' className='text-sm'>
                        Không theo chu kỳ
                      </label>
                    </div>
                    <div className='flex items-center justify-start'>
                      <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='yes' id='upgrade-yes' />
                        <label htmlFor='upgrade-yes' className='text-sm'>
                          Chu kỳ
                        </label>
                      </div>
                      <div>
                        <FormField
                          control={form.control}
                          name='autoUpgradeDays'
                          render={({ field }) => (
                            <FormItem>
                              <div className='ml-48 flex items-center gap-4'>
                                <FormControl>
                                  <Input
                                    {...field}
                                    type='number'
                                    disabled={isLoading}
                                    className='w-96'
                                    onChange={e => field.onChange(parseInt(e.target.value) || 0)}
                                  />
                                </FormControl>
                                <span className='text-sm'>ngày</span>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </RadioGroup>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Chu kỳ reset điểm */}
        <FormField
          control={form.control}
          name='resetPointsDays'
          render={({ field }) => (
            <FormItem>
              <div className='flex flex-col gap-4'>
                <FormLabel className='w-full text-sm font-medium'>Chu kỳ reset điểm</FormLabel>
                <div className='flex flex-1 items-center gap-2'>
                  <FormControl>
                    <Input
                      {...field}
                      type='number'
                      disabled={isLoading}
                      className='w-96'
                      onChange={e => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <span className='text-sm'>ngày</span>
                </div>
              </div>
              <div className='text-xs text-gray-500'>Xóa điểm tích lũy của khách hàng sau 31 ngày chưa trở lại</div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Không tích điểm cho hóa đơn có nguồn đơn từ */}
        <FormField
          control={form.control}
          name='excludeInvoiceOrigins'
          render={() => (
            <FormItem>
              <div className='flex flex-col gap-4'>
                <FormLabel className='w-full text-sm font-medium'>
                  Không tích điểm cho hóa đơn có nguồn đơn từ
                </FormLabel>
                <FormControl>
                  <Select disabled={isLoading}>
                    <SelectTrigger className='w-96'>
                      <SelectValue placeholder='Chọn nguồn' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='pos'>POS</SelectItem>
                      <SelectItem value='online'>Online</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className='flex justify-start'>
          <Button type='button' disabled={isLoading}>
            Lưu
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
