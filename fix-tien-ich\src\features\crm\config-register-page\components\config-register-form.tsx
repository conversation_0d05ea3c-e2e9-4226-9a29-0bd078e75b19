import { useRef } from 'react'

import { X } from 'lucide-react'

import { useCurrentBrand } from '@/stores/posStore'

import { useCrmImageUpload } from '@/hooks/crm'

import { Button, Checkbox, Label, RadioGroup, RadioGroupItem } from '@/components/ui'

import { useConfigRegister, type OtpMethod } from '../context'

export default function ConfigRegisterForm() {
  const {
    otpMethod,
    setOtpMethod,
    fields,
    updateField,
    isLoading,
    isSaving,
    onSave,
    logo,
    banner,
    settings,
    setLogo,
    setBanner
  } = useConfigRegister()

  const logoInputRef = useRef<HTMLInputElement>(null)
  const bannerInputRef = useRef<HTMLInputElement>(null)
  const { selectedBrand } = useCurrentBrand()
  const { uploadImage: uploadCrmImage, isUploading } = useCrmImageUpload({
    pos_parent: selectedBrand?.brandId || '',
    onSuccess: response => {
      console.log('Image uploaded successfully', response)
    },
    onError: error => {
      console.log('Image upload failed', error)
    }
  })

  const handleImageUpload = async (file: File, type: 'logo' | 'banner') => {
    if (!file || !file.type.startsWith('image/')) {
      return
    }

    try {
      const response = await uploadCrmImage(file)

      if (response?.data) {
        const imageUrl = response.data
        if (type === 'logo') setLogo(imageUrl)
        else setBanner(imageUrl)
      }
    } catch (error) {
      const reader = new FileReader()
      reader.onload = e => {
        const result = e.target?.result as string
        if (type === 'logo') {
          setLogo(result)
        } else {
          setBanner(result)
        }
      }
      reader.readAsDataURL(file)
    }
  }

  const handleLogoClick = () => {
    logoInputRef.current?.click()
  }

  const handleBannerClick = () => {
    bannerInputRef.current?.click()
  }

  const handleLogoRemove = () => {
    const originalLogo = settings?.Logo_Image || ''
    setLogo(originalLogo)
  }

  const handleBannerRemove = () => {
    const originalBanner = settings?.image || ''
    setBanner(originalBanner)
  }

  return (
    <>
      <div className='text-base font-medium'>Cài đặt form đăng ký</div>

      <div className='rounded-lg bg-gray-50 p-4'>
        <div className='space-y-6'>
          <div>
            <div className='text-base font-medium'>Phương thức xác thực</div>
            <div className='text-muted-foreground text-sm'>Chọn kênh gửi OTP, hoặc bỏ qua bước xác thực OTP</div>
            <div className='mt-3'>
              <RadioGroup
                value={otpMethod}
                onValueChange={v => setOtpMethod(v as OtpMethod)}
                className='grid gap-2 sm:grid-cols-3'
              >
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='sms' id='otp-sms' />
                  <Label htmlFor='otp-sms'>SMS</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='zns' id='otp-zns' disabled />
                  <Label htmlFor='otp-zns'>ZNS</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='none' id='otp-none' />
                  <Label htmlFor='otp-none'>Không xác thực</Label>
                </div>
              </RadioGroup>
            </div>
          </div>

          <div>
            <div className='text-base font-medium'>Các trường thông tin trong form đăng ký</div>
            <div className='text-muted-foreground text-sm'>Chọn các dữ liệu bạn muốn thu thập từ khách hàng</div>

            <div className='mt-4 space-y-3'>
              {fields.map(f => (
                <div key={f.field_id} className='grid items-center gap-2 sm:grid-cols-3'>
                  <Label className='text-sm font-medium' htmlFor={`field-${f.field_id}`}>
                    {f.field_name}
                  </Label>
                  <div className='flex items-center gap-4'>
                    <div className='flex items-center space-x-2'>
                      <Checkbox
                        id={`visible-${f.field_id}`}
                        checked={f.field_id === 'phone' ? true : !!f.active}
                        onCheckedChange={checked => updateField(f.field_id, { active: checked ? 1 : 0 })}
                        disabled={f.field_id === 'phone'}
                      />
                      <Label htmlFor={`visible-${f.field_id}`}>Hiển thị</Label>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Checkbox
                        id={`required-${f.field_id}`}
                        checked={f.field_id === 'phone' ? true : !!f.require}
                        onCheckedChange={checked => updateField(f.field_id, { require: !!checked })}
                        disabled={f.field_id === 'phone'}
                      />
                      <Label htmlFor={`required-${f.field_id}`}>Bắt buộc</Label>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className='space-y-2'>
            <div className='grid items-center gap-2 sm:grid-cols-3'>
              <Label className='cursor-pointer' onClick={handleLogoClick}>
                Logo
              </Label>
            </div>
            <div className='text-muted-foreground pl-0 text-sm'>Kích thước tối thiểu 100px x 100px, tỉ lệ 1:1</div>

            <input
              ref={logoInputRef}
              type='file'
              accept='image/*'
              className='hidden'
              onChange={e => {
                const file = e.target.files?.[0]
                handleImageUpload?.(file as File, 'logo')
              }}
            />

            <div className='relative mt-2 w-fit'>
              <img
                src={logo}
                alt='Logo'
                className='h-[100px] w-[100px] cursor-pointer rounded border-2 border-gray-300 hover:border-gray-400'
                onClick={handleLogoClick}
              />
              <Button
                type='button'
                variant='ghost'
                size='icon'
                className='absolute top-1 right-1 h-6 w-6 rounded-full bg-black/50 text-white hover:bg-black/75 hover:text-white'
                onClick={handleLogoRemove}
              >
                <X className='h-4 w-4' />
              </Button>
            </div>
          </div>

          <div className='space-y-2'>
            <div className='grid items-center gap-2 sm:grid-cols-3'>
              <Label className='cursor-pointer' onClick={handleBannerClick}>
                Banner
              </Label>
            </div>

            <input
              ref={bannerInputRef}
              type='file'
              accept='image/*'
              className='hidden'
              onChange={e => {
                const file = e.target.files?.[0]
                handleImageUpload?.(file as File, 'banner')
              }}
            />

            {banner && (
              <div className='relative mt-2 w-fit'>
                <img
                  src={banner}
                  alt='Banner'
                  width={150}
                  height={150}
                  className='cursor-pointer rounded border-2 border-gray-300 hover:border-gray-400'
                  onClick={handleBannerClick}
                />
                <Button
                  type='button'
                  variant='ghost'
                  size='icon'
                  className='absolute top-1 right-1 h-6 w-6 rounded-full bg-black/50 text-white hover:bg-black/75 hover:text-white'
                  onClick={handleBannerRemove}
                >
                  <X className='h-4 w-4' />
                </Button>
              </div>
            )}
          </div>

          <Button type='button' onClick={onSave} disabled={!!isSaving || !!isLoading || !!isUploading}>
            {isSaving ? 'Đang lưu...' : isUploading ? 'Đang upload...' : 'Lưu cấu hình'}
          </Button>
        </div>
      </div>
    </>
  )
}
