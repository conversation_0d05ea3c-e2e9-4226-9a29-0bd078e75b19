import { useState, useMemo } from 'react'

import type { Brand, City, Store } from '@/types/auth'

import { useAuthStore } from '@/stores/authStore'

interface BrandWithCities extends Brand {
  cities: (City & { stores: Store[] })[]
}

interface UseStoreSelectorProps {
  selectedStore: string
}

export function useStoreSelector({ selectedStore }: UseStoreSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedBrands, setExpandedBrands] = useState<Set<string>>(new Set())
  const [localSelectedStore, setLocalSelectedStore] = useState<string>(selectedStore)

  const { brands, cities, stores } = useAuthStore(state => state.auth)

  const hierarchicalData = useMemo((): BrandWithCities[] => {
    if (!brands || !cities || !stores) return []

    return brands
      .map((brand): BrandWithCities => {
        const brandCities = cities
          .filter(city => city.active === 1)
          .map(city => ({
            ...city,
            stores: stores.filter(
              store => store.active === 1 && store.brand_uid === brand.id && store.city_uid === city.id
            )
          }))
          .filter(city => city.stores.length > 0)

        return {
          ...brand,
          cities: brandCities
        }
      })
      .filter(brand => brand.cities.length > 0)
  }, [brands, cities, stores])

  const filteredData = useMemo(() => {
    if (!searchTerm) return hierarchicalData

    return hierarchicalData
      .map(brand => ({
        ...brand,
        cities: brand.cities
          .map(city => ({
            ...city,
            stores: city.stores.filter(store => store.store_name.toLowerCase().includes(searchTerm.toLowerCase()))
          }))
          .filter(city => city.city_name.toLowerCase().includes(searchTerm.toLowerCase()) || city.stores.length > 0)
      }))
      .filter(brand => brand.brand_name.toLowerCase().includes(searchTerm.toLowerCase()) || brand.cities.length > 0)
  }, [hierarchicalData, searchTerm])

  const toggleBrandExpansion = (brandId: string) => {
    const newExpanded = new Set(expandedBrands)
    if (newExpanded.has(brandId)) {
      newExpanded.delete(brandId)
    } else {
      newExpanded.add(brandId)
    }
    setExpandedBrands(newExpanded)
  }

  const resetSelection = () => {
    setLocalSelectedStore(selectedStore)
  }

  return {
    searchTerm,
    setSearchTerm,
    hierarchicalData,
    filteredData,
    expandedBrands,
    toggleBrandExpansion,
    localSelectedStore,
    setLocalSelectedStore,
    resetSelection
  }
}
