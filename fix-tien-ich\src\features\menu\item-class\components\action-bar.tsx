import { ReactNode } from 'react'

import { Button, Input } from '@/components/ui'

interface ActionBarProps {
  title: string
  searchValue: string
  searchPlaceholder: string
  onSearchChange: (value: string) => void
  onSearchKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
  actionButton?: {
    label: string
    icon?: ReactNode
    onClick: () => void
  }
}

export const ActionBar = ({
  title,
  searchValue,
  searchPlaceholder,
  onSearchChange,
  onSearchKeyDown,
  actionButton
}: ActionBarProps) => {
  return (
    <div className='mb-2 flex items-center justify-between'>
      <div className='flex items-center gap-4'>
        <h2 className='text-2xl font-semibold'>{title}</h2>
        <Input
          placeholder={searchPlaceholder}
          className='w-64'
          value={searchValue}
          onChange={e => onSearchChange(e.target.value)}
          onKeyDown={onSearchKeyDown}
        />
      </div>
      {actionButton && (
        <Button size='sm' onClick={actionButton.onClick}>
          {actionButton.icon && <span className='mr-2'>{actionButton.icon}</span>}
          {actionButton.label}
        </Button>
      )}
    </div>
  )
}
