import { ReportsSourcesData } from '@/types/api'
import { Bar, BarChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'

// Transform API data to chart format
const transformSourcesData = (sourcesData: ReportsSourcesData[]) => {
  return sourcesData
    .map(source => ({
      name: source.source_name,
      total: source.revenue_net
    }))
    .sort((a, b) => b.total - a.total) // Sort by revenue_net descending
    .slice(0, 5) // Take top 5
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const value = payload[0].value
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('vi-VN').format(amount)
    }

    return (
      <div className='rounded-lg border bg-white p-3 shadow-lg'>
        <p className='font-semibold text-gray-900'>{label}</p>
        <p className='text-xs text-gray-600'><PERSON><PERSON><PERSON> thu từ nguồn: {formatCurrency(value)} đ</p>
      </div>
    )
  }

  return null
}

interface SourcesOverviewProps {
  sourcesData?: ReportsSourcesData[]
}

export function SourcesOverview({ sourcesData }: SourcesOverviewProps) {
  // Check if no data available
  if (!sourcesData || sourcesData.length === 0) {
    return (
      <div className='flex h-[350px] items-center justify-center'>
        <div className='text-muted-foreground text-sm'>Chưa có thông tin</div>
      </div>
    )
  }

  const chartData = transformSourcesData(sourcesData)

  return (
    <ResponsiveContainer width='100%' height={350}>
      <BarChart data={chartData}>
        <XAxis
          dataKey='name'
          stroke='#888888'
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tick={{ fontSize: 12, textAnchor: 'middle' }}
          interval={0}
        />
        <YAxis
          stroke='#888888'
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={value => `${(value / 1000000).toFixed(0)}M`}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey='total' fill='currentColor' radius={[4, 4, 0, 0]} className='fill-primary' />
      </BarChart>
    </ResponsiveContainer>
  )
}
